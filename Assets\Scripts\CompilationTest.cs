using UnityEngine;

/// <summary>
/// Simple test script to verify Unity compilation works
/// If this compiles, Unity is working properly
/// </summary>
public class CompilationTest : MonoBehaviour
{
    [Header("Test Settings")]
    public bool testEnabled = true;
    public string testMessage = "Unity compilation working!";
    
    void Start()
    {
        if (testEnabled)
        {
            Debug.Log("✅ COMPILATION TEST PASSED: " + testMessage);
            Debug.Log("✅ Unity is compiling scripts correctly");
            Debug.Log("✅ MonoBehaviour inheritance working");
            Debug.Log("✅ Debug.Log working");
            Debug.Log("✅ All basic Unity features functional");
        }
    }
    
    void Update()
    {
        // Simple test that uses common Unity features
        if (testEnabled && Input.GetKeyDown(KeyCode.C))
        {
            Debug.Log("✅ Input system working - C key pressed");
            Debug.Log("✅ Time.time: " + Time.time);
            Debug.Log("✅ Transform position: " + transform.position);
        }
    }
}
