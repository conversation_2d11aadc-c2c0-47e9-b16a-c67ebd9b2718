using System.Collections.Generic;
using UnityEngine;
using RichBillionaire.Zones;

namespace RichBillionaire.Manager
{
    /// <summary>
    /// Manages player resources including money, materials, influence, and other currencies
    /// Tracks income, expenses, and resource generation from zones and pawns
    /// </summary>
    public class PlayerResourceTracker : MonoBehaviour
    {
        [Header("Starting Resources")]
        [SerializeField] private float startingMoney = 10000f;
        [SerializeField] private float startingMaterials = 500f;
        [SerializeField] private float startingInfluence = 100f;

        [Header("Current Resources")]
        public float money;
        public float materials;
        public float food;
        public float culture;
        public float faith;
        public float technology;
        public float happiness;
        public float environment;
        public float health;
        public float influence;
        public float corruption;

        [Header("Resource Limits")]
        public float maxMoney = 1000000f;
        public float maxMaterials = 10000f;
        public float maxFood = 5000f;
        public float maxCulture = 1000f;
        public float maxFaith = 1000f;
        public float maxTechnology = 1000f;
        public float maxHappiness = 1000f;
        public float maxEnvironment = 1000f;
        public float maxHealth = 1000f;
        public float maxInfluence = 1000f;
        public float maxCorruption = 1000f;

        [Header("Income Tracking")]
        public float totalIncomeGenerated = 0f;
        public float totalExpenses = 0f;
        public float dailyIncome = 0f;
        public float dailyExpenses = 0f;

        // Resource change tracking
        private Dictionary<ResourceType, float> resourceTotals = new Dictionary<ResourceType, float>();
        private Dictionary<ResourceType, float> dailyResourceGains = new Dictionary<ResourceType, float>();

        // Events
        public global::System.Action<ResourceType, float, float> OnResourceChanged; // type, oldValue, newValue
        public global::System.Action<float> OnMoneyChanged;
        public global::System.Action<string> OnResourceAlert; // For warnings about low resources

        // Time tracking for daily calculations
        private float lastDayTime;
        private float dayLength = 60f; // 1 minute = 1 game day

        void Start()
        {
            InitializeResources();
            InitializeResourceTracking();
            lastDayTime = Time.time;
        }

        void Update()
        {
            // Reset daily counters every game day
            if (Time.time - lastDayTime >= dayLength)
            {
                CalculateDailyTotals();
                ResetDailyCounters();
                lastDayTime = Time.time;
            }

            // Check for resource alerts
            CheckResourceAlerts();
        }

        /// <summary>
        /// Initializes starting resources
        /// </summary>
        private void InitializeResources()
        {
            money = startingMoney;
            materials = startingMaterials;
            influence = startingInfluence;

            // Initialize other resources to small amounts
            food = 100f;
            culture = 50f;
            faith = 50f;
            technology = 25f;
            happiness = 200f;
            environment = 300f;
            health = 150f;
            corruption = 0f;
        }

        /// <summary>
        /// Initializes resource tracking dictionaries
        /// </summary>
        private void InitializeResourceTracking()
        {
            foreach (ResourceType resourceType in System.Enum.GetValues(typeof(ResourceType)))
            {
                resourceTotals[resourceType] = 0f;
                dailyResourceGains[resourceType] = 0f;
            }
        }

        /// <summary>
        /// Adds resources of a specific type
        /// </summary>
        public bool AddResource(ResourceType resourceType, float amount)
        {
            if (amount <= 0) return false;

            float oldValue = GetResourceValue(resourceType);
            float maxValue = GetResourceMax(resourceType);
            float newValue = Mathf.Min(oldValue + amount, maxValue);

            SetResourceValue(resourceType, newValue);

            // Track totals
            resourceTotals[resourceType] += amount;
            dailyResourceGains[resourceType] += amount;

            if (resourceType == ResourceType.Money)
            {
                totalIncomeGenerated += amount;
            }

            OnResourceChanged?.Invoke(resourceType, oldValue, newValue);

            if (resourceType == ResourceType.Money)
            {
                OnMoneyChanged?.Invoke(newValue);
            }

            return true;
        }

        /// <summary>
        /// Spends/removes resources of a specific type
        /// </summary>
        public bool SpendResource(ResourceType resourceType, float amount)
        {
            if (amount <= 0) return false;

            float currentValue = GetResourceValue(resourceType);
            if (currentValue < amount) return false;

            float oldValue = currentValue;
            float newValue = currentValue - amount;

            SetResourceValue(resourceType, newValue);

            if (resourceType == ResourceType.Money)
            {
                totalExpenses += amount;
                dailyExpenses += amount;
            }

            OnResourceChanged?.Invoke(resourceType, oldValue, newValue);

            if (resourceType == ResourceType.Money)
            {
                OnMoneyChanged?.Invoke(newValue);
            }

            return true;
        }

        /// <summary>
        /// Checks if player has enough of a resource
        /// </summary>
        public bool HasResource(ResourceType resourceType, float amount)
        {
            return GetResourceValue(resourceType) >= amount;
        }

        /// <summary>
        /// Gets the current value of a resource
        /// </summary>
        public float GetResourceValue(ResourceType resourceType)
        {
            switch (resourceType)
            {
                case ResourceType.Money: return money;
                case ResourceType.Materials: return materials;
                case ResourceType.Food: return food;
                case ResourceType.Culture: return culture;
                case ResourceType.Faith: return faith;
                case ResourceType.Technology: return technology;
                case ResourceType.Happiness: return happiness;
                case ResourceType.Environment: return environment;
                case ResourceType.Health: return health;
                case ResourceType.Influence: return influence;
                case ResourceType.Corruption: return corruption;
                default: return 0f;
            }
        }

        /// <summary>
        /// Sets the value of a resource
        /// </summary>
        private void SetResourceValue(ResourceType resourceType, float value)
        {
            switch (resourceType)
            {
                case ResourceType.Money: money = value; break;
                case ResourceType.Materials: materials = value; break;
                case ResourceType.Food: food = value; break;
                case ResourceType.Culture: culture = value; break;
                case ResourceType.Faith: faith = value; break;
                case ResourceType.Technology: technology = value; break;
                case ResourceType.Happiness: happiness = value; break;
                case ResourceType.Environment: environment = value; break;
                case ResourceType.Health: health = value; break;
                case ResourceType.Influence: influence = value; break;
                case ResourceType.Corruption: corruption = value; break;
            }
        }

        /// <summary>
        /// Gets the maximum value for a resource
        /// </summary>
        private float GetResourceMax(ResourceType resourceType)
        {
            switch (resourceType)
            {
                case ResourceType.Money: return maxMoney;
                case ResourceType.Materials: return maxMaterials;
                case ResourceType.Food: return maxFood;
                case ResourceType.Culture: return maxCulture;
                case ResourceType.Faith: return maxFaith;
                case ResourceType.Technology: return maxTechnology;
                case ResourceType.Happiness: return maxHappiness;
                case ResourceType.Environment: return maxEnvironment;
                case ResourceType.Health: return maxHealth;
                case ResourceType.Influence: return maxInfluence;
                case ResourceType.Corruption: return maxCorruption;
                default: return 1000f;
            }
        }

        /// <summary>
        /// Calculates daily income and expense totals
        /// </summary>
        private void CalculateDailyTotals()
        {
            dailyIncome = 0f;
            foreach (var gain in dailyResourceGains.Values)
            {
                dailyIncome += gain;
            }
        }

        /// <summary>
        /// Resets daily counters
        /// </summary>
        private void ResetDailyCounters()
        {
            dailyExpenses = 0f;
            foreach (ResourceType resourceType in (ResourceType[])System.Enum.GetValues(typeof(ResourceType)))
            {
                dailyResourceGains[resourceType] = 0f;
            }
        }

        /// <summary>
        /// Checks for low resource alerts
        /// </summary>
        private void CheckResourceAlerts()
        {
            if (money < 1000f)
            {
                OnResourceAlert?.Invoke("Low on money! Consider increasing income sources.");
            }

            if (food < 50f)
            {
                OnResourceAlert?.Invoke("Food shortage! Build more farms or trade for food.");
            }

            if (happiness < 100f)
            {
                OnResourceAlert?.Invoke("Population unhappy! Build entertainment zones or improve conditions.");
            }

            if (corruption > 500f)
            {
                OnResourceAlert?.Invoke("High corruption detected! Take action to reduce criminal influence.");
            }
        }

        /// <summary>
        /// Gets a summary of all resources
        /// </summary>
        public string GetResourceSummary()
        {
            string summary = "=== RESOURCE SUMMARY ===\n";
            summary += $"Money: ${money:F0} / ${maxMoney:F0}\n";
            summary += $"Materials: {materials:F0} / {maxMaterials:F0}\n";
            summary += $"Food: {food:F0} / {maxFood:F0}\n";
            summary += $"Culture: {culture:F0} / {maxCulture:F0}\n";
            summary += $"Faith: {faith:F0} / {maxFaith:F0}\n";
            summary += $"Technology: {technology:F0} / {maxTechnology:F0}\n";
            summary += $"Happiness: {happiness:F0} / {maxHappiness:F0}\n";
            summary += $"Environment: {environment:F0} / {maxEnvironment:F0}\n";
            summary += $"Health: {health:F0} / {maxHealth:F0}\n";
            summary += $"Influence: {influence:F0} / {maxInfluence:F0}\n";
            summary += $"Corruption: {corruption:F0} / {maxCorruption:F0}\n";
            summary += $"\nDaily Income: ${dailyIncome:F0}\n";
            summary += $"Daily Expenses: ${dailyExpenses:F0}\n";
            summary += $"Net Daily: ${dailyIncome - dailyExpenses:F0}";

            return summary;
        }

        /// <summary>
        /// Gets total resources generated of a specific type
        /// </summary>
        public float GetTotalGenerated(ResourceType resourceType)
        {
            return resourceTotals.ContainsKey(resourceType) ? resourceTotals[resourceType] : 0f;
        }
    }
}
