using System.Collections.Generic;
using UnityEngine;
using RichBillionaire.Pawns;
using RichBillionaire.Zones;

namespace RichBillionaire.Manager
{
    /// <summary>
    /// Manages tax collection from pawns and income distribution
    /// Handles government policies, tax rates, and economic incentives
    /// </summary>
    public class TaxAndIncomeSystem : MonoBehaviour
    {
        [Header("Tax Settings")]
        [Range(0f, 50f)] public float incomeTaxRate = 15f;        // Percentage of pawn income
        [Range(0f, 30f)] public float corporateTaxRate = 10f;     // Percentage of zone income
        [Range(0f, 20f)] public float luxuryTaxRate = 5f;         // Tax on luxury spending
        [Range(0f, 100f)] public float corruptionTaxRate = 50f;   // Tax on illegal activities

        [Header("Government Policies")]
        public bool universalBasicIncome = false;
        [Range(0f, 100f)] public float basicIncomeAmount = 25f;
        public bool healthcareSubsidy = false;
        [Range(0f, 50f)] public float healthcareSubsidyRate = 10f;
        public bool educationSubsidy = false;
        [Range(0f, 50f)] public float educationSubsidyRate = 15f;

        [Header("Economic Incentives")]
        public bool entrepreneurshipBonus = false;
        [Range(0f, 50f)] public float entrepreneurshipBonusRate = 20f;
        public bool environmentalIncentive = false;
        [Range(0f, 30f)] public float environmentalIncentiveRate = 15f;
        public bool culturalInvestment = false;
        [Range(0f, 25f)] public float culturalInvestmentRate = 10f;

        [Header("Tax Collection Stats")]
        public float totalTaxesCollected = 0f;
        public float dailyTaxRevenue = 0f;
        public float totalSubsidiesPaid = 0f;
        public float dailySubsidies = 0f;

        // References
        private PlayerResourceTracker resourceTracker;
        private PawnGenerator pawnGenerator;
        private List<Zone> zones = new List<Zone>();

        // Tax collection tracking
        private float lastTaxCollectionTime;
        private float taxCollectionInterval = 30f; // Collect taxes every 30 seconds
        private Dictionary<Pawn, float> pawnTaxHistory = new Dictionary<Pawn, float>();

        // Events
        public System.Action<float> OnTaxesCollected;
        public System.Action<float> OnSubsidiesPaid;
        public System.Action<string> OnPolicyChanged;

        void Start()
        {
            // Find required components
            resourceTracker = FindObjectOfType<PlayerResourceTracker>();
            pawnGenerator = FindObjectOfType<PawnGenerator>();
            
            // Find all zones
            zones.AddRange(FindObjectsOfType<Zone>());

            lastTaxCollectionTime = Time.time;
        }

        void Update()
        {
            // Collect taxes periodically
            if (Time.time - lastTaxCollectionTime >= taxCollectionInterval)
            {
                CollectTaxes();
                PaySubsidies();
                lastTaxCollectionTime = Time.time;
            }
        }

        /// <summary>
        /// Collects taxes from all pawns and zones
        /// </summary>
        public void CollectTaxes()
        {
            if (resourceTracker == null) return;

            float totalCollected = 0f;
            
            // Collect income tax from pawns
            totalCollected += CollectIncomeTax();
            
            // Collect corporate tax from zones
            totalCollected += CollectCorporateTax();
            
            // Collect luxury tax
            totalCollected += CollectLuxuryTax();
            
            // Collect corruption tax
            totalCollected += CollectCorruptionTax();

            // Add to resource tracker
            if (totalCollected > 0)
            {
                resourceTracker.AddResource(ResourceType.Money, totalCollected);
                totalTaxesCollected += totalCollected;
                dailyTaxRevenue = totalCollected;
                
                OnTaxesCollected?.Invoke(totalCollected);
                Debug.Log($"Collected ${totalCollected:F0} in taxes");
            }
        }

        /// <summary>
        /// Collects income tax from all pawns
        /// </summary>
        private float CollectIncomeTax()
        {
            if (pawnGenerator == null) return 0f;

            float totalIncomeTax = 0f;
            var pawns = pawnGenerator.GetAllPawns();

            foreach (Pawn pawn in pawns)
            {
                if (pawn == null) continue;

                float pawnIncome = pawn.dailyIncome * (taxCollectionInterval / 86400f); // Proportional to collection interval
                float taxAmount = pawnIncome * (incomeTaxRate / 100f);
                
                // Reduce pawn's personal money
                pawn.personalMoney = Mathf.Max(0, pawn.personalMoney - (int)taxAmount);
                
                totalIncomeTax += taxAmount;
                
                // Track tax history
                if (!pawnTaxHistory.ContainsKey(pawn))
                {
                    pawnTaxHistory[pawn] = 0f;
                }
                pawnTaxHistory[pawn] += taxAmount;

                // Pawn reaction to taxes
                if (taxAmount > pawn.dailyIncome * 0.3f) // High tax burden
                {
                    pawn.overallSatisfaction -= 5f;
                }
            }

            return totalIncomeTax;
        }

        /// <summary>
        /// Collects corporate tax from zones
        /// </summary>
        private float CollectCorporateTax()
        {
            float totalCorporateTax = 0f;

            foreach (Zone zone in zones)
            {
                if (zone == null) continue;

                float zoneIncome = zone.currentResourceGeneration * (taxCollectionInterval / 86400f);
                float taxAmount = zoneIncome * (corporateTaxRate / 100f);
                
                totalCorporateTax += taxAmount;
            }

            return totalCorporateTax;
        }

        /// <summary>
        /// Collects luxury tax from high-spending pawns
        /// </summary>
        private float CollectLuxuryTax()
        {
            if (pawnGenerator == null) return 0f;

            float totalLuxuryTax = 0f;
            var pawns = pawnGenerator.GetAllPawns();

            foreach (Pawn pawn in pawns)
            {
                if (pawn == null) continue;

                // Tax based on luxury needs satisfaction and materialism
                if (pawn.luxuryNeeds > 70f && pawn.stats.materialism > 30)
                {
                    float luxurySpending = pawn.personalMoney * 0.1f; // Assume 10% spent on luxury
                    float taxAmount = luxurySpending * (luxuryTaxRate / 100f);
                    
                    pawn.personalMoney = Mathf.Max(0, pawn.personalMoney - (int)taxAmount);
                    totalLuxuryTax += taxAmount;
                }
            }

            return totalLuxuryTax;
        }

        /// <summary>
        /// Collects tax from corruption and illegal activities
        /// </summary>
        private float CollectCorruptionTax()
        {
            if (resourceTracker == null) return 0f;

            float corruptionLevel = resourceTracker.GetResourceValue(ResourceType.Corruption);
            float taxAmount = corruptionLevel * (corruptionTaxRate / 100f) * (taxCollectionInterval / 86400f);
            
            // Reduce corruption when taxed (represents law enforcement)
            resourceTracker.SpendResource(ResourceType.Corruption, taxAmount * 0.5f);
            
            return taxAmount;
        }

        /// <summary>
        /// Pays government subsidies and benefits
        /// </summary>
        public void PaySubsidies()
        {
            if (resourceTracker == null) return;

            float totalSubsidies = 0f;
            
            // Universal Basic Income
            if (universalBasicIncome)
            {
                totalSubsidies += PayUniversalBasicIncome();
            }
            
            // Healthcare Subsidy
            if (healthcareSubsidy)
            {
                totalSubsidies += PayHealthcareSubsidy();
            }
            
            // Education Subsidy
            if (educationSubsidy)
            {
                totalSubsidies += PayEducationSubsidy();
            }
            
            // Economic Incentives
            totalSubsidies += PayEconomicIncentives();

            // Deduct from government funds
            if (totalSubsidies > 0)
            {
                if (resourceTracker.HasResource(ResourceType.Money, totalSubsidies))
                {
                    resourceTracker.SpendResource(ResourceType.Money, totalSubsidies);
                    totalSubsidiesPaid += totalSubsidies;
                    dailySubsidies = totalSubsidies;
                    
                    OnSubsidiesPaid?.Invoke(totalSubsidies);
                    Debug.Log($"Paid ${totalSubsidies:F0} in subsidies");
                }
                else
                {
                    Debug.Log("Insufficient funds for subsidies!");
                }
            }
        }

        /// <summary>
        /// Pays universal basic income to all eligible pawns
        /// </summary>
        private float PayUniversalBasicIncome()
        {
            if (pawnGenerator == null) return 0f;

            float totalUBI = 0f;
            var pawns = pawnGenerator.GetAllPawns();

            foreach (Pawn pawn in pawns)
            {
                if (pawn == null || pawn.age < 18) continue; // Only adults eligible

                pawn.personalMoney += (int)basicIncomeAmount;
                pawn.basicNeeds += 5f; // UBI helps with basic needs
                totalUBI += basicIncomeAmount;
            }

            return totalUBI;
        }

        /// <summary>
        /// Pays healthcare subsidies
        /// </summary>
        private float PayHealthcareSubsidy()
        {
            if (pawnGenerator == null) return 0f;

            float totalHealthcare = 0f;
            var pawns = pawnGenerator.GetAllPawns();

            foreach (Pawn pawn in pawns)
            {
                if (pawn == null) continue;

                float subsidyAmount = pawn.dailyIncome * (healthcareSubsidyRate / 100f);
                pawn.basicNeeds += 3f; // Healthcare improves basic needs
                totalHealthcare += subsidyAmount;
            }

            return totalHealthcare;
        }

        /// <summary>
        /// Pays education subsidies
        /// </summary>
        private float PayEducationSubsidy()
        {
            if (pawnGenerator == null) return 0f;

            float totalEducation = 0f;
            var pawns = pawnGenerator.GetAllPawns();

            foreach (Pawn pawn in pawns)
            {
                if (pawn == null || pawn.age > 25) continue; // Young people benefit most

                float subsidyAmount = basicIncomeAmount * (educationSubsidyRate / 100f);
                pawn.stats.intellect += 1; // Education slowly improves intellect
                totalEducation += subsidyAmount;
            }

            return totalEducation;
        }

        /// <summary>
        /// Pays economic incentives for specific behaviors
        /// </summary>
        private float PayEconomicIncentives()
        {
            float totalIncentives = 0f;

            // Entrepreneurship bonus
            if (entrepreneurshipBonus && pawnGenerator != null)
            {
                var pawns = pawnGenerator.GetAllPawns();
                foreach (Pawn pawn in pawns)
                {
                    if (pawn != null && pawn.stats.ambition > 50 && pawn.primaryDesire == PrimaryDesire.Wealth)
                    {
                        float bonus = pawn.dailyIncome * (entrepreneurshipBonusRate / 100f);
                        pawn.personalMoney += (int)bonus;
                        totalIncentives += bonus;
                    }
                }
            }

            // Environmental incentive
            if (environmentalIncentive && resourceTracker != null)
            {
                float envLevel = resourceTracker.GetResourceValue(ResourceType.Environment);
                if (envLevel > 200f)
                {
                    float incentive = envLevel * (environmentalIncentiveRate / 100f);
                    totalIncentives += incentive;
                }
            }

            // Cultural investment
            if (culturalInvestment && resourceTracker != null)
            {
                float cultureLevel = resourceTracker.GetResourceValue(ResourceType.Culture);
                if (cultureLevel > 100f)
                {
                    float investment = cultureLevel * (culturalInvestmentRate / 100f);
                    totalIncentives += investment;
                }
            }

            return totalIncentives;
        }

        /// <summary>
        /// Changes a tax rate and notifies listeners
        /// </summary>
        public void SetTaxRate(string taxType, float newRate)
        {
            switch (taxType.ToLower())
            {
                case "income":
                    incomeTaxRate = Mathf.Clamp(newRate, 0f, 50f);
                    break;
                case "corporate":
                    corporateTaxRate = Mathf.Clamp(newRate, 0f, 30f);
                    break;
                case "luxury":
                    luxuryTaxRate = Mathf.Clamp(newRate, 0f, 20f);
                    break;
                case "corruption":
                    corruptionTaxRate = Mathf.Clamp(newRate, 0f, 100f);
                    break;
            }
            
            OnPolicyChanged?.Invoke($"{taxType} tax rate changed to {newRate:F1}%");
        }

        /// <summary>
        /// Toggles a government policy
        /// </summary>
        public void TogglePolicy(string policyName)
        {
            switch (policyName.ToLower())
            {
                case "ubi":
                case "universalbasicincome":
                    universalBasicIncome = !universalBasicIncome;
                    OnPolicyChanged?.Invoke($"Universal Basic Income {(universalBasicIncome ? "enabled" : "disabled")}");
                    break;
                case "healthcare":
                    healthcareSubsidy = !healthcareSubsidy;
                    OnPolicyChanged?.Invoke($"Healthcare Subsidy {(healthcareSubsidy ? "enabled" : "disabled")}");
                    break;
                case "education":
                    educationSubsidy = !educationSubsidy;
                    OnPolicyChanged?.Invoke($"Education Subsidy {(educationSubsidy ? "enabled" : "disabled")}");
                    break;
                case "entrepreneurship":
                    entrepreneurshipBonus = !entrepreneurshipBonus;
                    OnPolicyChanged?.Invoke($"Entrepreneurship Bonus {(entrepreneurshipBonus ? "enabled" : "disabled")}");
                    break;
                case "environmental":
                    environmentalIncentive = !environmentalIncentive;
                    OnPolicyChanged?.Invoke($"Environmental Incentive {(environmentalIncentive ? "enabled" : "disabled")}");
                    break;
                case "cultural":
                    culturalInvestment = !culturalInvestment;
                    OnPolicyChanged?.Invoke($"Cultural Investment {(culturalInvestment ? "enabled" : "disabled")}");
                    break;
            }
        }

        /// <summary>
        /// Gets economic summary
        /// </summary>
        public string GetEconomicSummary()
        {
            float netRevenue = dailyTaxRevenue - dailySubsidies;
            
            string summary = "=== ECONOMIC SUMMARY ===\n";
            summary += $"Tax Rates: Income {incomeTaxRate:F1}%, Corporate {corporateTaxRate:F1}%, Luxury {luxuryTaxRate:F1}%\n";
            summary += $"Daily Tax Revenue: ${dailyTaxRevenue:F0}\n";
            summary += $"Daily Subsidies: ${dailySubsidies:F0}\n";
            summary += $"Net Daily Revenue: ${netRevenue:F0}\n";
            summary += $"Total Taxes Collected: ${totalTaxesCollected:F0}\n";
            summary += $"Total Subsidies Paid: ${totalSubsidiesPaid:F0}\n";
            summary += $"\nActive Policies:\n";
            if (universalBasicIncome) summary += "- Universal Basic Income\n";
            if (healthcareSubsidy) summary += "- Healthcare Subsidy\n";
            if (educationSubsidy) summary += "- Education Subsidy\n";
            if (entrepreneurshipBonus) summary += "- Entrepreneurship Bonus\n";
            if (environmentalIncentive) summary += "- Environmental Incentive\n";
            if (culturalInvestment) summary += "- Cultural Investment\n";
            
            return summary;
        }
    }
}
