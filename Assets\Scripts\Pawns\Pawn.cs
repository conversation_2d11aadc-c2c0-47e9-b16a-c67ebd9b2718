using System;
using UnityEngine;

namespace RichBillionaire.Pawns
{
    /// <summary>
    /// Core pawn class representing individual citizens in the game
    /// Pawns have stats, needs, desires, and generate resources based on their work
    /// </summary>
    [Serializable]
    public class Pawn : MonoBehaviour
    {
        [Header("Identity")]
        public string pawnName;
        public string ethnicity;
        public int age;
        public Gender gender;
        
        [Header("Core Stats")]
        public PawnStats stats;
        
        [<PERSON><PERSON>("Needs & Satisfaction")]
        [Range(0, 100)] public float basicNeeds = 50f;        // Food, shelter, safety
        [Range(0, 100)] public float luxuryNeeds = 20f;       // Entertainment, comfort
        [Range(0, 100)] public float socialNeeds = 30f;       // Relationships, community
        [Range(0, 100)] public float overallSatisfaction = 40f;
        
        [Header("Desires & Behavior")]
        public PrimaryDesire primaryDesire;
        [Range(0, 100)] public int impulsiveness = 50;
        [Range(0, 100)] public int personalMoney = 100;
        
        [<PERSON><PERSON>("Work & Productivity")]
        public ZoneType currentWorkZone = ZoneType.Industrial;
        public float productivityMultiplier = 1.0f;
        public float dailyIncome = 50f;
        
        [Header("Relationships")]
        public int friendsCount = 0;
        public int enemiesCount = 0;
        public bool hasPartner = false;
        public int childrenCount = 0;

        // Events
        public event Action<Pawn> OnPawnDied;
        public event Action<Pawn, float> OnIncomeGenerated;
        public event Action<Pawn, string> OnPawnAction;

        // Private fields
        private float lastIncomeTime;
        private float incomeInterval = 24f; // 24 seconds = 1 game day
        
        void Start()
        {
            if (stats == null)
            {
                stats = new PawnStats(true);
            }
            
            CalculateProductivity();
            lastIncomeTime = Time.time;
        }
        
        void Update()
        {
            // Generate income over time
            if (Time.time - lastIncomeTime >= incomeInterval)
            {
                GenerateIncome();
                lastIncomeTime = Time.time;
            }
            
            // Update needs over time
            UpdateNeeds();
            
            // Calculate overall satisfaction
            CalculateSatisfaction();
        }

        /// <summary>
        /// Initializes a new pawn with given parameters
        /// </summary>
        public void Initialize(string name, string ethnicity, Gender gender, int age)
        {
            this.pawnName = name;
            this.ethnicity = ethnicity;
            this.gender = gender;
            this.age = age;
            
            stats = new PawnStats(true);
            primaryDesire = (PrimaryDesire)UnityEngine.Random.Range(0, Enum.GetValues(typeof(PrimaryDesire)).Length);
            impulsiveness = UnityEngine.Random.Range(20, 81);
            personalMoney = UnityEngine.Random.Range(50, 201);
            
            // Set initial needs based on stats
            basicNeeds = UnityEngine.Random.Range(30, 71);
            luxuryNeeds = UnityEngine.Random.Range(10, 51);
            socialNeeds = 50 + stats.sociability * 0.3f;
            
            CalculateProductivity();
        }

        /// <summary>
        /// Calculates productivity based on stats and current work zone
        /// </summary>
        public void CalculateProductivity()
        {
            productivityMultiplier = stats.GetZoneEffectiveness(currentWorkZone);
            
            // Age affects productivity
            if (age < 18) productivityMultiplier *= 0.5f;
            else if (age > 65) productivityMultiplier *= 0.7f;
            else if (age >= 25 && age <= 45) productivityMultiplier *= 1.2f; // Prime working age
            
            // Satisfaction affects productivity
            productivityMultiplier *= (0.5f + overallSatisfaction * 0.01f);
            
            // Calculate daily income
            dailyIncome = 50f * productivityMultiplier;
        }

        /// <summary>
        /// Generates income for the pawn and triggers events
        /// </summary>
        private void GenerateIncome()
        {
            float income = dailyIncome;
            personalMoney += (int)income;
            
            OnIncomeGenerated?.Invoke(this, income);
            
            // Random events based on personality
            if (UnityEngine.Random.value < impulsiveness * 0.001f)
            {
                TriggerImpulsiveAction();
            }
        }

        /// <summary>
        /// Updates pawn needs over time
        /// </summary>
        private void UpdateNeeds()
        {
            float deltaTime = Time.deltaTime;
            
            // Basic needs decay faster
            basicNeeds -= deltaTime * 2f;
            
            // Luxury needs decay slower
            luxuryNeeds -= deltaTime * 1f;
            
            // Social needs depend on personality
            float socialDecay = 1.5f - (stats.sociability * 0.01f);
            socialNeeds -= deltaTime * socialDecay;
            
            // Clamp values
            basicNeeds = Mathf.Clamp(basicNeeds, 0, 100);
            luxuryNeeds = Mathf.Clamp(luxuryNeeds, 0, 100);
            socialNeeds = Mathf.Clamp(socialNeeds, 0, 100);
        }

        /// <summary>
        /// Calculates overall satisfaction based on needs and personality
        /// </summary>
        private void CalculateSatisfaction()
        {
            overallSatisfaction = stats.CalculateHappiness(basicNeeds * 0.01f, luxuryNeeds * 0.01f) * 100f;
            
            // Social needs impact
            overallSatisfaction += (socialNeeds - 50f) * 0.3f;
            
            // Clamp satisfaction
            overallSatisfaction = Mathf.Clamp(overallSatisfaction, 0, 100);
            
            // Recalculate productivity when satisfaction changes significantly
            CalculateProductivity();
        }

        /// <summary>
        /// Triggers an impulsive action based on personality
        /// </summary>
        private void TriggerImpulsiveAction()
        {
            string action = "";
            
            if (stats.materialism > 30 && personalMoney > 100)
            {
                action = "bought something expensive";
                personalMoney -= UnityEngine.Random.Range(50, 150);
                luxuryNeeds += UnityEngine.Random.Range(10, 30);
            }
            else if (stats.sociability > 30)
            {
                action = "went out with friends";
                personalMoney -= UnityEngine.Random.Range(20, 60);
                socialNeeds += UnityEngine.Random.Range(15, 35);
            }
            else if (stats.morality < -30)
            {
                action = "did something questionable";
                personalMoney += UnityEngine.Random.Range(30, 100);
                overallSatisfaction -= UnityEngine.Random.Range(5, 15);
            }
            
            if (!string.IsNullOrEmpty(action))
            {
                OnPawnAction?.Invoke(this, action);
            }
        }

        /// <summary>
        /// Assigns pawn to work in a specific zone
        /// </summary>
        public void AssignToZone(ZoneType zoneType)
        {
            currentWorkZone = zoneType;
            CalculateProductivity();
            OnPawnAction?.Invoke(this, $"started working in {zoneType}");
        }

        /// <summary>
        /// Satisfies pawn's basic needs (housing, food, etc.)
        /// </summary>
        public void SatisfyBasicNeeds(float amount)
        {
            basicNeeds = Mathf.Min(100, basicNeeds + amount);
        }

        /// <summary>
        /// Satisfies pawn's luxury needs (entertainment, comfort, etc.)
        /// </summary>
        public void SatisfyLuxuryNeeds(float amount)
        {
            luxuryNeeds = Mathf.Min(100, luxuryNeeds + amount);
        }

        /// <summary>
        /// Gets a summary of the pawn's current state
        /// </summary>
        public string GetStatusSummary()
        {
            return $"{pawnName} ({age}y {gender}) - Satisfaction: {overallSatisfaction:F0}% - Income: ${dailyIncome:F0}/day - {stats.GetPersonalityDescription()}";
        }
    }

    public enum Gender
    {
        Male,
        Female,
        NonBinary
    }

    public enum PrimaryDesire
    {
        Wealth,         // Wants to accumulate money and possessions
        Power,          // Wants to control others and situations
        Knowledge,      // Wants to learn and understand
        Love,           // Wants relationships and family
        Fame,           // Wants recognition and status
        Freedom,        // Wants independence and autonomy
        Security,       // Wants safety and stability
        Adventure,      // Wants excitement and new experiences
        Creativity,     // Wants to create and express
        Service         // Wants to help others and society
    }
}
