using System.Collections.Generic;
using UnityEngine;

namespace RichBillionaire.Pawns
{
    /// <summary>
    /// Auto-generator system for creating diverse starting pawns
    /// Creates realistic populations with varied demographics and stats
    /// </summary>
    public class PawnGenerator : MonoBehaviour
    {
        [Header("Generation Settings")]
        [SerializeField] private int defaultPawnCount = 20;
        [SerializeField] private GameObject pawnPrefab;
        [SerializeField] private Transform pawnContainer;

        [Header("Age Distribution")]
        [Range(0, 100)] public float childPercentage = 15f;      // 0-17
        [Range(0, 100)] public float youngAdultPercentage = 25f; // 18-30
        [Range(0, 100)] public float adultPercentage = 35f;      // 31-50
        [Range(0, 100)] public float middleAgedPercentage = 20f; // 51-65
        [Range(0, 100)] public float elderlyPercentage = 5f;     // 66+

        [Header("Gender Distribution")]
        [Range(0, 100)] public float malePercentage = 48f;
        [Range(0, 100)] public float femalePercentage = 48f;
        [Range(0, 100)] public float nonBinaryPercentage = 4f;

        [Header("Portrait Settings")]
        [SerializeField] private Sprite[] malePortraits;
        [SerializeField] private Sprite[] femalePortraits;
        [SerializeField] private Sprite[] nonBinaryPortraits;

        // Generated pawns list
        private List<Pawn> generatedPawns = new List<Pawn>();

        // Events
        public global::System.Action<List<Pawn>> OnPawnsGenerated;

        void Start()
        {
            if (generatedPawns.Count == 0)
            {
                GenerateStartingPawns(defaultPawnCount);
            }
        }

        /// <summary>
        /// Generates a specified number of starting pawns with diverse characteristics
        /// </summary>
        public List<Pawn> GenerateStartingPawns(int count)
        {
            generatedPawns.Clear();

            for (int i = 0; i < count; i++)
            {
                Pawn newPawn = GenerateSinglePawn();
                if (newPawn != null)
                {
                    generatedPawns.Add(newPawn);
                }
            }

            Debug.Log($"Generated {generatedPawns.Count} pawns");
            OnPawnsGenerated?.Invoke(generatedPawns);

            return new List<Pawn>(generatedPawns);
        }

        /// <summary>
        /// Generates a single pawn with random characteristics
        /// </summary>
        public Pawn GenerateSinglePawn()
        {
            // Create pawn GameObject
            GameObject pawnObj;
            if (pawnPrefab != null)
            {
                pawnObj = Instantiate(pawnPrefab, pawnContainer);
            }
            else
            {
                pawnObj = new GameObject("Generated Pawn");
                pawnObj.transform.SetParent(pawnContainer);
            }

            // Add Pawn component if it doesn't exist
            Pawn pawn = pawnObj.GetComponent<Pawn>();
            if (pawn == null)
            {
                pawn = pawnObj.AddComponent<Pawn>();
            }

            // Generate characteristics
            Gender gender = GenerateRandomGender();
            string ethnicity = PawnNameDatabase.Instance.GetRandomEthnicity();
            string name = PawnNameDatabase.Instance.GetRandomName(gender, ethnicity);
            int age = GenerateRandomAge();

            // Initialize the pawn
            pawn.Initialize(name, ethnicity, gender, age);

            // Assign portrait
            AssignPortrait(pawn, gender);

            // Set random work zone
            ZoneType[] zoneTypes = global::System.Enum.GetValues(typeof(ZoneType)) as ZoneType[];
            ZoneType randomZone = zoneTypes[Random.Range(0, zoneTypes.Length)];
            pawn.AssignToZone(randomZone);

            // Add some randomness to starting conditions
            RandomizeStartingConditions(pawn);

            Debug.Log($"Generated pawn: {pawn.GetStatusSummary()}");

            return pawn;
        }

        /// <summary>
        /// Generates random gender based on distribution percentages
        /// </summary>
        private Gender GenerateRandomGender()
        {
            float roll = Random.Range(0f, 100f);
            float cumulative = 0f;

            cumulative += malePercentage;
            if (roll <= cumulative) return Gender.Male;

            cumulative += femalePercentage;
            if (roll <= cumulative) return Gender.Female;

            return Gender.NonBinary;
        }

        /// <summary>
        /// Generates random age based on distribution percentages
        /// </summary>
        private int GenerateRandomAge()
        {
            float roll = Random.Range(0f, 100f);
            float cumulative = 0f;

            cumulative += childPercentage;
            if (roll <= cumulative) return Random.Range(5, 18);

            cumulative += youngAdultPercentage;
            if (roll <= cumulative) return Random.Range(18, 31);

            cumulative += adultPercentage;
            if (roll <= cumulative) return Random.Range(31, 51);

            cumulative += middleAgedPercentage;
            if (roll <= cumulative) return Random.Range(51, 66);

            return Random.Range(66, 85);
        }

        /// <summary>
        /// Assigns appropriate portrait based on gender
        /// </summary>
        private void AssignPortrait(Pawn pawn, Gender gender)
        {
            Sprite[] portraitArray = null;

            switch (gender)
            {
                case Gender.Male:
                    portraitArray = malePortraits;
                    break;
                case Gender.Female:
                    portraitArray = femalePortraits;
                    break;
                case Gender.NonBinary:
                    portraitArray = nonBinaryPortraits;
                    break;
            }

            if (portraitArray != null && portraitArray.Length > 0)
            {
                SpriteRenderer spriteRenderer = pawn.GetComponent<SpriteRenderer>();
                if (spriteRenderer == null)
                {
                    spriteRenderer = pawn.gameObject.AddComponent<SpriteRenderer>();
                }

                spriteRenderer.sprite = portraitArray[Random.Range(0, portraitArray.Length)];
            }
        }

        /// <summary>
        /// Adds some randomness to starting conditions for variety
        /// </summary>
        private void RandomizeStartingConditions(Pawn pawn)
        {
            // Random starting money variation
            pawn.personalMoney += Random.Range(-50, 101);
            pawn.personalMoney = Mathf.Max(0, pawn.personalMoney);

            // Random starting needs variation
            pawn.basicNeeds += Random.Range(-20, 21);
            pawn.luxuryNeeds += Random.Range(-15, 16);
            pawn.socialNeeds += Random.Range(-25, 26);

            // Clamp needs
            pawn.basicNeeds = Mathf.Clamp(pawn.basicNeeds, 0, 100);
            pawn.luxuryNeeds = Mathf.Clamp(pawn.luxuryNeeds, 0, 100);
            pawn.socialNeeds = Mathf.Clamp(pawn.socialNeeds, 0, 100);

            // Random relationships
            if (pawn.age >= 18)
            {
                pawn.friendsCount = Random.Range(0, 8);
                pawn.enemiesCount = Random.Range(0, 3);

                if (pawn.age >= 25 && Random.value < 0.4f)
                {
                    pawn.hasPartner = true;
                }

                if (pawn.hasPartner && pawn.age >= 30 && Random.value < 0.6f)
                {
                    pawn.childrenCount = Random.Range(1, 4);
                }
            }
        }

        /// <summary>
        /// Gets all currently generated pawns
        /// </summary>
        public List<Pawn> GetAllPawns()
        {
            return new List<Pawn>(generatedPawns);
        }

        /// <summary>
        /// Adds a new pawn to the population
        /// </summary>
        public void AddPawn(Pawn pawn)
        {
            if (pawn != null && !generatedPawns.Contains(pawn))
            {
                generatedPawns.Add(pawn);
            }
        }

        /// <summary>
        /// Removes a pawn from the population
        /// </summary>
        public void RemovePawn(Pawn pawn)
        {
            if (pawn != null && generatedPawns.Contains(pawn))
            {
                generatedPawns.Remove(pawn);
                if (pawn.gameObject != null)
                {
                    DestroyImmediate(pawn.gameObject);
                }
            }
        }

        /// <summary>
        /// Gets population statistics
        /// </summary>
        public string GetPopulationStats()
        {
            if (generatedPawns.Count == 0) return "No pawns generated";

            int males = 0, females = 0, nonBinary = 0;
            int children = 0, adults = 0, elderly = 0;
            float avgSatisfaction = 0f;

            foreach (Pawn pawn in generatedPawns)
            {
                // Gender count
                switch (pawn.gender)
                {
                    case Gender.Male: males++; break;
                    case Gender.Female: females++; break;
                    case Gender.NonBinary: nonBinary++; break;
                }

                // Age groups
                if (pawn.age < 18) children++;
                else if (pawn.age < 65) adults++;
                else elderly++;

                avgSatisfaction += pawn.overallSatisfaction;
            }

            avgSatisfaction /= generatedPawns.Count;

            return $"Population: {generatedPawns.Count}\n" +
                   $"Gender: {males}M, {females}F, {nonBinary}NB\n" +
                   $"Age: {children} children, {adults} adults, {elderly} elderly\n" +
                   $"Avg Satisfaction: {avgSatisfaction:F1}%";
        }
    }
}
