using System.Collections.Generic;
using UnityEngine;

namespace RichBillionaire.Pawns
{
    /// <summary>
    /// Database of names from different ethnic backgrounds for pawn generation
    /// Provides diverse, culturally appropriate names for realistic population simulation
    /// </summary>
    [CreateAssetMenu(fileName = "PawnNameDatabase", menuName = "Rich Billionaire/Pawn Name Database")]
    public class PawnNameDatabase : ScriptableObject
    {
        [Header("Male Names by Ethnicity")]
        public List<string> blackMaleNames = new List<string>();
        public List<string> whiteMaleNames = new List<string>();
        public List<string> hispanicMaleNames = new List<string>();
        public List<string> asianMaleNames = new List<string>();
        public List<string> indianMaleNames = new List<string>();
        public List<string> arabicMaleNames = new List<string>();

        [Header("Female Names by Ethnicity")]
        public List<string> blackFemaleNames = new List<string>();
        public List<string> whiteFemaleNames = new List<string>();
        public List<string> hispanicFemaleNames = new List<string>();
        public List<string> asianFemaleNames = new List<string>();
        public List<string> indianFemaleNames = new List<string>();
        public List<string> arabicFemaleNames = new List<string>();

        [Header("Last Names by Ethnicity")]
        public List<string> blackLastNames = new List<string>();
        public List<string> whiteLastNames = new List<string>();
        public List<string> hispanicLastNames = new List<string>();
        public List<string> asianLastNames = new List<string>();
        public List<string> indianLastNames = new List<string>();
        public List<string> arabicLastNames = new List<string>();

        [Header("Ethnicity Distribution")]
        [Range(0, 100)] public float blackPercentage = 15f;
        [Range(0, 100)] public float whitePercentage = 40f;
        [Range(0, 100)] public float hispanicPercentage = 20f;
        [Range(0, 100)] public float asianPercentage = 15f;
        [Range(0, 100)] public float indianPercentage = 5f;
        [Range(0, 100)] public float arabicPercentage = 5f;

        private static PawnNameDatabase instance;
        public static PawnNameDatabase Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = CreateDefaultDatabase();
                }
                return instance;
            }
        }

        /// <summary>
        /// Gets a random name based on gender and ethnicity
        /// </summary>
        public string GetRandomName(Gender gender, string ethnicity)
        {
            List<string> firstNames = GetFirstNameList(gender, ethnicity);
            List<string> lastNames = GetLastNameList(ethnicity);

            if (firstNames.Count == 0 || lastNames.Count == 0)
            {
                return "Unknown Person";
            }

            string firstName = firstNames[Random.Range(0, firstNames.Count)];
            string lastName = lastNames[Random.Range(0, lastNames.Count)];

            return $"{firstName} {lastName}";
        }

        /// <summary>
        /// Gets a random ethnicity based on distribution percentages
        /// </summary>
        public string GetRandomEthnicity()
        {
            float roll = Random.Range(0f, 100f);
            float cumulative = 0f;

            cumulative += blackPercentage;
            if (roll <= cumulative) return "Black";

            cumulative += whitePercentage;
            if (roll <= cumulative) return "White";

            cumulative += hispanicPercentage;
            if (roll <= cumulative) return "Hispanic";

            cumulative += asianPercentage;
            if (roll <= cumulative) return "Asian";

            cumulative += indianPercentage;
            if (roll <= cumulative) return "Indian";

            return "Arabic";
        }

        private List<string> GetFirstNameList(Gender gender, string ethnicity)
        {
            bool isMale = (gender == Gender.Male);
            
            switch (ethnicity.ToLower())
            {
                case "black": return isMale ? blackMaleNames : blackFemaleNames;
                case "white": return isMale ? whiteMaleNames : whiteFemaleNames;
                case "hispanic": return isMale ? hispanicMaleNames : hispanicFemaleNames;
                case "asian": return isMale ? asianMaleNames : asianFemaleNames;
                case "indian": return isMale ? indianMaleNames : indianFemaleNames;
                case "arabic": return isMale ? arabicMaleNames : arabicFemaleNames;
                default: return isMale ? whiteMaleNames : whiteFemaleNames;
            }
        }

        private List<string> GetLastNameList(string ethnicity)
        {
            switch (ethnicity.ToLower())
            {
                case "black": return blackLastNames;
                case "white": return whiteLastNames;
                case "hispanic": return hispanicLastNames;
                case "asian": return asianLastNames;
                case "indian": return indianLastNames;
                case "arabic": return arabicLastNames;
                default: return whiteLastNames;
            }
        }

        /// <summary>
        /// Creates a default database with sample names if none exists
        /// </summary>
        private static PawnNameDatabase CreateDefaultDatabase()
        {
            PawnNameDatabase db = CreateInstance<PawnNameDatabase>();
            
            // Sample names - in a real implementation, these would be much more extensive
            db.blackMaleNames.AddRange(new[] { "Marcus", "Jamal", "Darius", "Terrell", "Andre", "Malik", "Xavier", "Isaiah" });
            db.blackFemaleNames.AddRange(new[] { "Keisha", "Tamara", "Alicia", "Jasmine", "Ebony", "Tiffany", "Monique", "Shanice" });
            db.blackLastNames.AddRange(new[] { "Johnson", "Williams", "Brown", "Jones", "Davis", "Miller", "Wilson", "Moore" });

            db.whiteMaleNames.AddRange(new[] { "Michael", "David", "John", "James", "Robert", "William", "Richard", "Thomas" });
            db.whiteFemaleNames.AddRange(new[] { "Jennifer", "Lisa", "Michelle", "Amy", "Angela", "Heather", "Stephanie", "Nicole" });
            db.whiteLastNames.AddRange(new[] { "Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller", "Davis" });

            db.hispanicMaleNames.AddRange(new[] { "Carlos", "Jose", "Luis", "Miguel", "Antonio", "Francisco", "Manuel", "Jorge" });
            db.hispanicFemaleNames.AddRange(new[] { "Maria", "Carmen", "Rosa", "Ana", "Isabel", "Elena", "Sofia", "Lucia" });
            db.hispanicLastNames.AddRange(new[] { "Garcia", "Rodriguez", "Martinez", "Lopez", "Gonzalez", "Hernandez", "Perez", "Sanchez" });

            db.asianMaleNames.AddRange(new[] { "Wei", "Chen", "Li", "Zhang", "Hiroshi", "Takeshi", "Kenji", "Akira" });
            db.asianFemaleNames.AddRange(new[] { "Mei", "Lin", "Yuki", "Sakura", "Akiko", "Emiko", "Xiaoli", "Jing" });
            db.asianLastNames.AddRange(new[] { "Wang", "Li", "Zhang", "Liu", "Chen", "Yang", "Huang", "Zhao" });

            db.indianMaleNames.AddRange(new[] { "Raj", "Amit", "Vikram", "Arjun", "Ravi", "Suresh", "Anil", "Deepak" });
            db.indianFemaleNames.AddRange(new[] { "Priya", "Sunita", "Kavita", "Meera", "Anjali", "Pooja", "Rekha", "Sita" });
            db.indianLastNames.AddRange(new[] { "Patel", "Sharma", "Singh", "Kumar", "Gupta", "Agarwal", "Jain", "Mehta" });

            db.arabicMaleNames.AddRange(new[] { "Ahmed", "Mohammed", "Ali", "Omar", "Hassan", "Khalid", "Youssef", "Amr" });
            db.arabicFemaleNames.AddRange(new[] { "Fatima", "Aisha", "Zeinab", "Maryam", "Layla", "Nour", "Yasmin", "Hala" });
            db.arabicLastNames.AddRange(new[] { "Al-Ahmad", "Al-Mohammed", "Al-Hassan", "Al-Omar", "Al-Ali", "Al-Khalil", "Al-Rashid", "Al-Mansour" });

            return db;
        }

        /// <summary>
        /// Validates that all name lists have content
        /// </summary>
        public bool ValidateDatabase()
        {
            return blackMaleNames.Count > 0 && blackFemaleNames.Count > 0 && blackLastNames.Count > 0 &&
                   whiteMaleNames.Count > 0 && whiteFemaleNames.Count > 0 && whiteLastNames.Count > 0 &&
                   hispanicMaleNames.Count > 0 && hispanicFemaleNames.Count > 0 && hispanicLastNames.Count > 0 &&
                   asianMaleNames.Count > 0 && asianFemaleNames.Count > 0 && asianLastNames.Count > 0 &&
                   indianMaleNames.Count > 0 && indianFemaleNames.Count > 0 && indianLastNames.Count > 0 &&
                   arabicMaleNames.Count > 0 && arabicFemaleNames.Count > 0 && arabicLastNames.Count > 0;
        }
    }
}
