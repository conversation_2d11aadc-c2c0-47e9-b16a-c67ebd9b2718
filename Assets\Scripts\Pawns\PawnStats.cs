using System;
using UnityEngine;

namespace RichBillionaire.Pawns
{
    /// <summary>
    /// Polarity-based stats system for pawns
    /// Each stat ranges from -100 to +100, representing opposing traits
    /// </summary>
    [Serializable]
    public class PawnStats
    {
        [Header("Core Attributes")]
        [Range(-100, 100)] public int intellect = 0;      // Smart/Dumb
        [Range(-100, 100)] public int morality = 0;       // Good/Evil
        [Range(-100, 100)] public int ambition = 0;       // Driven/Lazy
        [Range(-100, 100)] public int strength = 0;       // Strong/Weak
        [Range(-100, 100)] public int sociability = 0;    // Social/Antisocial
        [Range(-100, 100)] public int creativity = 0;     // Creative/Conventional
        [Range(-100, 100)] public int stability = 0;      // Stable/Chaotic
        [Range(-100, 100)] public int materialism = 0;    // Materialistic/Spiritual

        [Header("Secondary Traits")]
        [Range(-100, 100)] public int loyalty = 0;        // Loyal/Rebellious
        [Range(-100, 100)] public int patience = 0;       // Patient/Impulsive
        [Range(-100, 100)] public int optimism = 0;       // Optimistic/Pessimistic
        [Range(-100, 100)] public int conformity = 0;     // Conformist/Independent

        public PawnStats()
        {
            // Default constructor creates neutral pawn
        }

        public PawnStats(bool randomize)
        {
            if (randomize)
            {
                RandomizeStats();
            }
        }

        /// <summary>
        /// Randomizes all stats with realistic distribution
        /// Most pawns will be closer to neutral with some having extreme traits
        /// </summary>
        public void RandomizeStats()
        {
            intellect = GenerateRandomStat();
            morality = GenerateRandomStat();
            ambition = GenerateRandomStat();
            strength = GenerateRandomStat();
            sociability = GenerateRandomStat();
            creativity = GenerateRandomStat();
            stability = GenerateRandomStat();
            materialism = GenerateRandomStat();
            loyalty = GenerateRandomStat();
            patience = GenerateRandomStat();
            optimism = GenerateRandomStat();
            conformity = GenerateRandomStat();
        }

        /// <summary>
        /// Generates a random stat with bell curve distribution
        /// 70% chance of being within -30 to +30 (moderate)
        /// 20% chance of being within -60 to +60 (strong)
        /// 10% chance of being extreme (-100 to +100)
        /// </summary>
        private int GenerateRandomStat()
        {
            float roll = UnityEngine.Random.value;
            
            if (roll < 0.7f)
            {
                // Moderate range
                return UnityEngine.Random.Range(-30, 31);
            }
            else if (roll < 0.9f)
            {
                // Strong range
                return UnityEngine.Random.Range(-60, 61);
            }
            else
            {
                // Extreme range
                return UnityEngine.Random.Range(-100, 101);
            }
        }

        /// <summary>
        /// Gets the effectiveness multiplier for a specific zone type
        /// </summary>
        public float GetZoneEffectiveness(ZoneType zoneType)
        {
            float effectiveness = 1.0f;

            switch (zoneType)
            {
                case ZoneType.Industrial:
                    effectiveness += (strength + ambition) * 0.01f;
                    break;
                case ZoneType.Farming:
                    effectiveness += (strength + patience + stability) * 0.005f;
                    break;
                case ZoneType.Cultural:
                    effectiveness += (creativity + sociability + intellect) * 0.005f;
                    break;
                case ZoneType.Mystic:
                    effectiveness += (-materialism + creativity + stability) * 0.005f;
                    break;
                case ZoneType.Vice:
                    effectiveness += (-morality + materialism - stability) * 0.005f;
                    break;
                case ZoneType.TechHub:
                    effectiveness += (intellect + creativity + ambition) * 0.005f;
                    break;
                case ZoneType.Entertainment:
                    effectiveness += (creativity + sociability + optimism) * 0.005f;
                    break;
                case ZoneType.Environmental:
                    effectiveness += (-materialism + morality + stability) * 0.005f;
                    break;
            }

            return Mathf.Max(0.1f, effectiveness); // Minimum 10% effectiveness
        }

        /// <summary>
        /// Gets a human-readable description of the pawn's personality
        /// </summary>
        public string GetPersonalityDescription()
        {
            string description = "This person is ";
            
            if (intellect > 30) description += "intelligent, ";
            else if (intellect < -30) description += "simple-minded, ";
            
            if (morality > 30) description += "virtuous, ";
            else if (morality < -30) description += "morally questionable, ";
            
            if (ambition > 30) description += "highly driven, ";
            else if (ambition < -30) description += "laid-back, ";
            
            if (sociability > 30) description += "very social, ";
            else if (sociability < -30) description += "introverted, ";
            
            if (creativity > 30) description += "creative, ";
            else if (creativity < -30) description += "conventional, ";

            // Remove trailing comma and space
            if (description.EndsWith(", "))
            {
                description = description.Substring(0, description.Length - 2);
            }
            
            description += ".";
            return description;
        }

        /// <summary>
        /// Calculates overall happiness based on current needs satisfaction
        /// </summary>
        public float CalculateHappiness(float basicNeedsSatisfaction, float luxuryNeedsSatisfaction)
        {
            float happiness = basicNeedsSatisfaction * 0.7f + luxuryNeedsSatisfaction * 0.3f;
            
            // Personality modifiers
            happiness += optimism * 0.001f;
            happiness -= Mathf.Abs(stability) * 0.0005f; // Unstable people are less happy
            
            return Mathf.Clamp01(happiness);
        }
    }
}

/// <summary>
/// Zone types that pawns can work in
/// Each type has different stat requirements for optimal performance
/// </summary>
public enum ZoneType
{
    Industrial,     // Manufacturing, construction
    Farming,        // Agriculture, food production
    Cultural,       // Arts, education, museums
    Mystic,         // Religious, spiritual centers
    Vice,           // Gambling, nightlife, adult entertainment
    TechHub,        // Technology, research, innovation
    Entertainment,  // Sports, media, recreation
    Environmental   // Parks, conservation, green energy
}
