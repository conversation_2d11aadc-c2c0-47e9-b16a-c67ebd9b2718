using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Simplified Game Manager that definitely compiles
/// Manages basic game state without complex dependencies
/// </summary>
public class SimpleGameManager : MonoBehaviour
{
    [Header("Game Settings")]
    public bool autoStart = true;
    public int targetFrameRate = 60;
    
    [Header("Game State")]
    public bool gameRunning = false;
    public float gameTime = 0f;
    public int gameDay = 1;
    
    // Simple pawn list
    private List<SimplePawn> pawns = new List<SimplePawn>();
    
    void Start()
    {
        Debug.Log("🎮 SimpleGameManager Starting...");
        
        // Set frame rate
        Application.targetFrameRate = targetFrameRate;
        
        if (autoStart)
        {
            StartGame();
        }
    }
    
    void Update()
    {
        if (gameRunning)
        {
            gameTime += Time.deltaTime;
            
            // Simple day counter (every 60 seconds = 1 day)
            int newDay = Mathf.FloorToInt(gameTime / 60f) + 1;
            if (newDay != gameDay)
            {
                gameDay = newDay;
                Debug.Log($"📅 Day {gameDay} started");
            }
        }
        
        // Simple controls
        if (Input.GetKeyDown(KeyCode.Space))
        {
            TogglePause();
        }
        
        if (Input.GetKeyDown(KeyCode.G))
        {
            CreateSimplePawn();
        }
    }
    
    public void StartGame()
    {
        gameRunning = true;
        gameTime = 0f;
        gameDay = 1;
        
        Debug.Log("✅ Game Started Successfully!");
        Debug.Log("Controls: SPACE = Pause, G = Create Pawn");
        
        // Create initial pawns
        for (int i = 0; i < 5; i++)
        {
            CreateSimplePawn();
        }
    }
    
    public void TogglePause()
    {
        gameRunning = !gameRunning;
        Time.timeScale = gameRunning ? 1f : 0f;
        Debug.Log(gameRunning ? "▶️ Game Resumed" : "⏸️ Game Paused");
    }
    
    public void CreateSimplePawn()
    {
        GameObject pawnObj = new GameObject($"Pawn_{pawns.Count + 1}");
        pawnObj.transform.SetParent(transform);
        
        // Position in a simple grid
        int index = pawns.Count;
        float x = (index % 5) * 2f - 4f;
        float y = (index / 5) * -2f + 2f;
        pawnObj.transform.position = new Vector3(x, y, 0);
        
        // Add simple pawn component
        SimplePawn pawn = pawnObj.AddComponent<SimplePawn>();
        pawn.Initialize($"Citizen_{index + 1}");
        
        pawns.Add(pawn);
        
        Debug.Log($"👤 Created {pawn.pawnName} at position ({x:F1}, {y:F1})");
    }
    
    public List<SimplePawn> GetAllPawns()
    {
        return new List<SimplePawn>(pawns);
    }
}

/// <summary>
/// Simplified Pawn class that definitely compiles
/// </summary>
public class SimplePawn : MonoBehaviour
{
    [Header("Pawn Info")]
    public string pawnName = "Unknown";
    public int age = 25;
    public float happiness = 50f;
    public float money = 100f;
    
    private SpriteRenderer spriteRenderer;
    
    void Start()
    {
        // Add visual representation
        spriteRenderer = gameObject.AddComponent<SpriteRenderer>();
        spriteRenderer.sprite = CreateSimpleSprite();
        spriteRenderer.color = GetRandomColor();
    }
    
    void Update()
    {
        // Simple happiness fluctuation
        happiness += Random.Range(-0.1f, 0.1f);
        happiness = Mathf.Clamp(happiness, 0f, 100f);
        
        // Update color based on happiness
        if (spriteRenderer != null)
        {
            float greenAmount = happiness / 100f;
            spriteRenderer.color = new Color(1f - greenAmount, greenAmount, 0.5f, 1f);
        }
    }
    
    public void Initialize(string name)
    {
        pawnName = name;
        age = Random.Range(18, 80);
        happiness = Random.Range(30f, 80f);
        money = Random.Range(50f, 500f);
        
        gameObject.name = pawnName;
    }
    
    private Sprite CreateSimpleSprite()
    {
        // Create a simple circle sprite
        Texture2D texture = new Texture2D(32, 32);
        Color[] pixels = new Color[32 * 32];
        
        Vector2 center = new Vector2(16, 16);
        float radius = 14f;
        
        for (int y = 0; y < 32; y++)
        {
            for (int x = 0; x < 32; x++)
            {
                Vector2 pos = new Vector2(x, y);
                float distance = Vector2.Distance(pos, center);
                
                if (distance <= radius)
                {
                    pixels[y * 32 + x] = Color.white;
                }
                else
                {
                    pixels[y * 32 + x] = Color.clear;
                }
            }
        }
        
        texture.SetPixels(pixels);
        texture.Apply();
        
        return Sprite.Create(texture, new Rect(0, 0, 32, 32), new Vector2(0.5f, 0.5f));
    }
    
    private Color GetRandomColor()
    {
        return new Color(Random.value, Random.value, Random.value, 1f);
    }
    
    void OnMouseDown()
    {
        Debug.Log($"👤 Clicked on {pawnName} - Age: {age}, Happiness: {happiness:F1}%, Money: ${money:F0}");
    }
}
