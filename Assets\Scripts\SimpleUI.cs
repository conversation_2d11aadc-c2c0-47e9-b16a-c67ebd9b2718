using UnityEngine;
using RichBillionaire.Pawns;
using RichBillionaire.Manager;

/// <summary>
/// Simple UI that actually works - displays game info using OnGUI
/// No complex dependencies, just shows the data
/// </summary>
public class SimpleUI : MonoBehaviour
{
    [Header("UI Settings")]
    public bool showUI = true;
    public int fontSize = 14;
    
    // System references
    private PawnGenerator pawnGenerator;
    private PlayerResourceTracker resourceTracker;
    private TaxAndIncomeSystem taxSystem;
    
    // Current pawn tracking
    private Pawn currentPawn;
    private int currentPawnIndex = 0;
    
    void Start()
    {
        // Find the systems
        pawnGenerator = FindObjectOfType<PawnGenerator>();
        resourceTracker = FindObjectOfType<PlayerResourceTracker>();
        taxSystem = FindObjectOfType<TaxAndIncomeSystem>();
        
        Debug.Log("✅ SimpleUI Started - Press H to toggle UI, Arrow keys to navigate pawns");
    }
    
    void Update()
    {
        // Handle input
        if (Input.GetKeyDown(KeyCode.H))
        {
            showUI = !showUI;
        }
        
        if (Input.GetKeyDown(KeyCode.LeftArrow))
        {
            NavigatePrevious();
        }
        
        if (Input.GetKeyDown(KeyCode.RightArrow))
        {
            NavigateNext();
        }
        
        if (Input.GetKeyDown(KeyCode.Space))
        {
            Time.timeScale = Time.timeScale > 0 ? 0 : 1;
            Debug.Log(Time.timeScale > 0 ? "Game Resumed" : "Game Paused");
        }
        
        // Update current pawn
        UpdateCurrentPawn();
    }
    
    void OnGUI()
    {
        if (!showUI) return;
        
        // Set up GUI style
        GUIStyle style = new GUIStyle(GUI.skin.label);
        style.fontSize = fontSize;
        style.normal.textColor = Color.white;
        
        // Background boxes
        GUI.Box(new Rect(10, 10, 300, 200), "");
        GUI.Box(new Rect(Screen.width - 310, 10, 300, 150), "");
        GUI.Box(new Rect(10, 220, 300, 100), "");
        
        // Draw pawn info
        DrawPawnInfo(style);
        
        // Draw resources
        DrawResources(style);
        
        // Draw controls
        DrawControls(style);
    }
    
    void DrawPawnInfo(GUIStyle style)
    {
        string info = "=== CURRENT PAWN ===\n";
        
        if (currentPawn != null)
        {
            info += $"Name: {currentPawn.pawnName}\n";
            info += $"Age: {currentPawn.age} | {currentPawn.gender}\n";
            info += $"Ethnicity: {currentPawn.ethnicity}\n";
            info += $"Job: {currentPawn.currentWorkZone}\n";
            info += $"Income: ${currentPawn.dailyIncome:F0}/day\n";
            info += $"Money: ${currentPawn.personalMoney}\n";
            info += $"Satisfaction: {currentPawn.overallSatisfaction:F0}%\n";
            info += $"Basic Needs: {currentPawn.basicNeeds:F0}%\n";
            info += $"Luxury Needs: {currentPawn.luxuryNeeds:F0}%\n";
            info += $"Social Needs: {currentPawn.socialNeeds:F0}%";
        }
        else
        {
            info += "No pawn selected\n";
            info += "Generating pawns...";
        }
        
        GUI.Label(new Rect(20, 20, 280, 180), info, style);
    }
    
    void DrawResources(GUIStyle style)
    {
        string info = "=== RESOURCES ===\n";
        
        if (resourceTracker != null)
        {
            info += $"Money: ${resourceTracker.money:F0}\n";
            info += $"Materials: {resourceTracker.materials:F0}\n";
            info += $"Food: {resourceTracker.food:F0}\n";
            info += $"Culture: {resourceTracker.culture:F0}\n";
            info += $"Happiness: {resourceTracker.happiness:F0}\n";
            info += $"Technology: {resourceTracker.technology:F0}\n";
            info += $"Environment: {resourceTracker.environment:F0}";
        }
        else
        {
            info += "Resource tracker not found";
        }
        
        GUI.Label(new Rect(Screen.width - 300, 20, 280, 130), info, style);
    }
    
    void DrawControls(GUIStyle style)
    {
        string info = "=== CONTROLS ===\n";
        info += $"← → : Navigate Pawns ({currentPawnIndex + 1}/{GetPawnCount()})\n";
        info += "SPACE: Pause/Resume\n";
        info += "H: Toggle UI\n";
        info += "G: Generate Pawn (if SimpleGameManager)\n";
        info += "T: Run Tests";
        
        GUI.Label(new Rect(20, 230, 280, 80), info, style);
    }
    
    void UpdateCurrentPawn()
    {
        if (pawnGenerator == null) return;
        
        var pawns = pawnGenerator.GetAllPawns();
        if (pawns.Count > 0)
        {
            currentPawnIndex = Mathf.Clamp(currentPawnIndex, 0, pawns.Count - 1);
            currentPawn = pawns[currentPawnIndex];
        }
    }
    
    void NavigatePrevious()
    {
        if (pawnGenerator == null) return;
        
        var pawns = pawnGenerator.GetAllPawns();
        if (pawns.Count > 0)
        {
            currentPawnIndex = (currentPawnIndex - 1 + pawns.Count) % pawns.Count;
            currentPawn = pawns[currentPawnIndex];
            Debug.Log($"Selected: {currentPawn.pawnName}");
        }
    }
    
    void NavigateNext()
    {
        if (pawnGenerator == null) return;
        
        var pawns = pawnGenerator.GetAllPawns();
        if (pawns.Count > 0)
        {
            currentPawnIndex = (currentPawnIndex + 1) % pawns.Count;
            currentPawn = pawns[currentPawnIndex];
            Debug.Log($"Selected: {currentPawn.pawnName}");
        }
    }
    
    int GetPawnCount()
    {
        if (pawnGenerator == null) return 0;
        return pawnGenerator.GetAllPawns().Count;
    }
}
