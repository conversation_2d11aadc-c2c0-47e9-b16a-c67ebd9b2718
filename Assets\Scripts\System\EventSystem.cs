using System.Collections.Generic;
using UnityEngine;
using RichBillionaire.Pawns;
using RichBillionaire.Manager;
using RichBillionaire.Zones;

namespace RichBillionaire.System
{
    /// <summary>
    /// Handles random events, choice events, and special occurrences in the game
    /// Provides hooks for future expansion with complex choice systems and NPC interactions
    /// </summary>
    public class EventSystem : MonoBehaviour
    {
        [Header("Event Settings")]
        [Range(0f, 1f)] public float dailyEventChance = 0.3f;
        [Range(0f, 1f)] public float majorEventChance = 0.05f;
        [Range(0f, 1f)] public float pawnEventChance = 0.2f;

        [Header("Event Impact Ranges")]
        [Range(1f, 100f)] public float minorEventImpact = 10f;
        [Range(50f, 500f)] public float majorEventImpact = 100f;

        // System references
        private PlayerResourceTracker resourceTracker;
        private PawnGenerator pawnGenerator;
        private TaxAndIncomeSystem taxSystem;

        // Event tracking
        private List<GameEvent> eventHistory = new List<GameEvent>();
        private Dictionary<EventType, float> eventCooldowns = new Dictionary<EventType, float>();

        // Events
        public global::System.Action<GameEvent> OnEventTriggered;
        public global::System.Action<string> OnEventMessage;

        void Start()
        {
            InitializeReferences();
            InitializeEventCooldowns();
        }

        /// <summary>
        /// Initializes references to other systems
        /// </summary>
        private void InitializeReferences()
        {
            resourceTracker = FindObjectOfType<PlayerResourceTracker>();
            pawnGenerator = FindObjectOfType<PawnGenerator>();
            taxSystem = FindObjectOfType<TaxAndIncomeSystem>();
        }

        /// <summary>
        /// Initializes event cooldown tracking
        /// </summary>
        private void InitializeEventCooldowns()
        {
            foreach (EventType eventType in (EventType[])System.Enum.GetValues(typeof(EventType)))
            {
                eventCooldowns[eventType] = 0f;
            }
        }

        /// <summary>
        /// Triggers daily events (called by GameManager)
        /// </summary>
        public void TriggerDailyEvents()
        {
            UpdateEventCooldowns();

            // Check for daily events
            if (Random.value < dailyEventChance)
            {
                TriggerRandomEvent();
            }

            // Check for major events
            if (Random.value < majorEventChance)
            {
                TriggerMajorEvent();
            }

            // Check for pawn-specific events
            if (Random.value < pawnEventChance)
            {
                TriggerPawnEvent();
            }
        }

        /// <summary>
        /// Updates event cooldowns
        /// </summary>
        private void UpdateEventCooldowns()
        {
            var keys = new List<EventType>(eventCooldowns.Keys);
            foreach (var key in keys)
            {
                if (eventCooldowns[key] > 0)
                {
                    eventCooldowns[key] -= 1f; // Reduce by 1 day
                }
            }
        }

        /// <summary>
        /// Triggers a random minor event
        /// </summary>
        private void TriggerRandomEvent()
        {
            EventType[] minorEvents = {
                EventType.MarketFluctuation,
                EventType.WeatherEvent,
                EventType.CulturalEvent,
                EventType.TechDiscovery,
                EventType.SocialTrend
            };

            EventType selectedEvent = minorEvents[Random.Range(0, minorEvents.Length)];

            if (eventCooldowns[selectedEvent] <= 0)
            {
                ExecuteEvent(selectedEvent, false);
                eventCooldowns[selectedEvent] = Random.Range(3f, 7f); // 3-7 day cooldown
            }
        }

        /// <summary>
        /// Triggers a major event with significant impact
        /// </summary>
        private void TriggerMajorEvent()
        {
            EventType[] majorEvents = {
                EventType.EconomicCrisis,
                EventType.NaturalDisaster,
                EventType.PoliticalUpheaval,
                EventType.TechnologicalBreakthrough,
                EventType.CulturalRevolution
            };

            EventType selectedEvent = majorEvents[Random.Range(0, majorEvents.Length)];

            if (eventCooldowns[selectedEvent] <= 0)
            {
                ExecuteEvent(selectedEvent, true);
                eventCooldowns[selectedEvent] = Random.Range(10f, 20f); // 10-20 day cooldown
            }
        }

        /// <summary>
        /// Triggers a pawn-specific event
        /// </summary>
        private void TriggerPawnEvent()
        {
            if (pawnGenerator == null) return;

            var pawns = pawnGenerator.GetAllPawns();
            if (pawns.Count == 0) return;

            Pawn selectedPawn = pawns[Random.Range(0, pawns.Count)];
            ExecutePawnEvent(selectedPawn);
        }

        /// <summary>
        /// Executes a specific event
        /// </summary>
        private void ExecuteEvent(EventType eventType, bool isMajor)
        {
            GameEvent gameEvent = new GameEvent
            {
                eventType = eventType,
                isMajor = isMajor,
                day = GameManager.Instance.gameDay,
                description = GetEventDescription(eventType, isMajor)
            };

            switch (eventType)
            {
                case EventType.MarketFluctuation:
                    ExecuteMarketFluctuation(isMajor);
                    break;
                case EventType.WeatherEvent:
                    ExecuteWeatherEvent(isMajor);
                    break;
                case EventType.CulturalEvent:
                    ExecuteCulturalEvent(isMajor);
                    break;
                case EventType.TechDiscovery:
                    ExecuteTechDiscovery(isMajor);
                    break;
                case EventType.SocialTrend:
                    ExecuteSocialTrend(isMajor);
                    break;
                case EventType.EconomicCrisis:
                    ExecuteEconomicCrisis();
                    break;
                case EventType.NaturalDisaster:
                    ExecuteNaturalDisaster();
                    break;
                case EventType.PoliticalUpheaval:
                    ExecutePoliticalUpheaval();
                    break;
                case EventType.TechnologicalBreakthrough:
                    ExecuteTechnologicalBreakthrough();
                    break;
                case EventType.CulturalRevolution:
                    ExecuteCulturalRevolution();
                    break;
            }

            eventHistory.Add(gameEvent);
            OnEventTriggered?.Invoke(gameEvent);
            OnEventMessage?.Invoke(gameEvent.description);

            Debug.Log($"Event triggered: {gameEvent.description}");
        }

        /// <summary>
        /// Executes a pawn-specific event
        /// </summary>
        private void ExecutePawnEvent(Pawn pawn)
        {
            if (pawn == null) return;

            string eventDescription = "";

            // Event based on pawn's personality and current state
            if (pawn.stats.ambition > 50 && pawn.personalMoney > 200)
            {
                // Entrepreneurial event
                pawn.personalMoney += Random.Range(100, 300);
                pawn.overallSatisfaction += Random.Range(10, 20);
                eventDescription = $"{pawn.pawnName} started a successful side business!";
            }
            else if (pawn.stats.morality < -30 && Random.value < 0.3f)
            {
                // Criminal activity
                pawn.personalMoney += Random.Range(50, 150);
                if (resourceTracker != null)
                {
                    resourceTracker.AddResource(ResourceType.Corruption, Random.Range(5, 15));
                }
                eventDescription = $"{pawn.pawnName} was involved in questionable activities...";
            }
            else if (pawn.stats.sociability > 50 && pawn.socialNeeds < 50)
            {
                // Social event
                pawn.socialNeeds += Random.Range(20, 40);
                pawn.friendsCount += Random.Range(1, 3);
                eventDescription = $"{pawn.pawnName} made new friends at a social gathering!";
            }
            else if (pawn.overallSatisfaction < 30)
            {
                // Unhappy pawn event
                if (Random.value < 0.5f)
                {
                    pawn.overallSatisfaction += Random.Range(15, 30);
                    eventDescription = $"{pawn.pawnName} had a stroke of good luck and feels better!";
                }
                else
                {
                    pawn.overallSatisfaction -= Random.Range(5, 15);
                    eventDescription = $"{pawn.pawnName} had a particularly bad day...";
                }
            }

            if (!string.IsNullOrEmpty(eventDescription))
            {
                OnEventMessage?.Invoke(eventDescription);
                Debug.Log($"Pawn event: {eventDescription}");
            }
        }

        // Event execution methods
        private void ExecuteMarketFluctuation(bool isMajor)
        {
            if (resourceTracker == null) return;

            float impact = isMajor ? majorEventImpact : minorEventImpact;
            bool positive = Random.value > 0.5f;

            if (positive)
            {
                resourceTracker.AddResource(ResourceType.Money, impact);
            }
            else
            {
                resourceTracker.SpendResource(ResourceType.Money, impact);
            }
        }

        private void ExecuteWeatherEvent(bool isMajor)
        {
            if (resourceTracker == null) return;

            float impact = isMajor ? majorEventImpact : minorEventImpact;

            // Weather affects food and environment
            if (Random.value > 0.5f) // Good weather
            {
                resourceTracker.AddResource(ResourceType.Food, impact * 0.5f);
                resourceTracker.AddResource(ResourceType.Environment, impact * 0.3f);
            }
            else // Bad weather
            {
                resourceTracker.SpendResource(ResourceType.Food, impact * 0.3f);
                resourceTracker.SpendResource(ResourceType.Environment, impact * 0.2f);
            }
        }

        private void ExecuteCulturalEvent(bool isMajor)
        {
            if (resourceTracker == null) return;

            float impact = isMajor ? majorEventImpact : minorEventImpact;
            resourceTracker.AddResource(ResourceType.Culture, impact);
            resourceTracker.AddResource(ResourceType.Happiness, impact * 0.5f);
        }

        private void ExecuteTechDiscovery(bool isMajor)
        {
            if (resourceTracker == null) return;

            float impact = isMajor ? majorEventImpact : minorEventImpact;
            resourceTracker.AddResource(ResourceType.Technology, impact);

            if (isMajor)
            {
                resourceTracker.AddResource(ResourceType.Money, impact * 0.5f);
            }
        }

        private void ExecuteSocialTrend(bool isMajor)
        {
            if (pawnGenerator == null) return;

            var pawns = pawnGenerator.GetAllPawns();
            float impact = isMajor ? 20f : 10f;

            foreach (var pawn in pawns)
            {
                if (Random.value < 0.3f) // 30% of pawns affected
                {
                    pawn.socialNeeds += Random.Range(-impact, impact);
                    pawn.socialNeeds = Mathf.Clamp(pawn.socialNeeds, 0, 100);
                }
            }
        }

        private void ExecuteEconomicCrisis()
        {
            if (resourceTracker == null || pawnGenerator == null) return;

            // Major economic impact
            resourceTracker.SpendResource(ResourceType.Money, majorEventImpact * 2f);

            // Affect all pawns
            var pawns = pawnGenerator.GetAllPawns();
            foreach (var pawn in pawns)
            {
                pawn.personalMoney = Mathf.Max(0, pawn.personalMoney - Random.Range(50, 150));
                pawn.overallSatisfaction -= Random.Range(10, 25);
            }
        }

        private void ExecuteNaturalDisaster()
        {
            if (resourceTracker == null) return;

            // Damage to multiple resources
            resourceTracker.SpendResource(ResourceType.Materials, majorEventImpact);
            resourceTracker.SpendResource(ResourceType.Environment, majorEventImpact * 0.5f);
            resourceTracker.SpendResource(ResourceType.Health, majorEventImpact * 0.3f);
        }

        private void ExecutePoliticalUpheaval()
        {
            if (resourceTracker == null || taxSystem == null) return;

            // Political instability affects influence and corruption
            resourceTracker.SpendResource(ResourceType.Influence, majorEventImpact * 0.5f);
            resourceTracker.AddResource(ResourceType.Corruption, majorEventImpact * 0.3f);

            // Random policy changes
            if (Random.value < 0.5f)
            {
                taxSystem.TogglePolicy("ubi");
            }
        }

        private void ExecuteTechnologicalBreakthrough()
        {
            if (resourceTracker == null) return;

            // Major tech advancement
            resourceTracker.AddResource(ResourceType.Technology, majorEventImpact * 2f);
            resourceTracker.AddResource(ResourceType.Money, majorEventImpact);
            resourceTracker.AddResource(ResourceType.Health, majorEventImpact * 0.3f);
        }

        private void ExecuteCulturalRevolution()
        {
            if (resourceTracker == null || pawnGenerator == null) return;

            // Major cultural shift
            resourceTracker.AddResource(ResourceType.Culture, majorEventImpact * 2f);

            // Affect pawn stats
            var pawns = pawnGenerator.GetAllPawns();
            foreach (var pawn in pawns)
            {
                if (Random.value < 0.4f) // 40% of pawns affected
                {
                    pawn.stats.creativity += Random.Range(-10, 20);
                    pawn.stats.conformity += Random.Range(-15, 10);
                }
            }
        }

        /// <summary>
        /// Gets a description for an event
        /// </summary>
        private string GetEventDescription(EventType eventType, bool isMajor)
        {
            string intensity = isMajor ? "Major" : "Minor";

            switch (eventType)
            {
                case EventType.MarketFluctuation:
                    return $"{intensity} market fluctuation affects the economy";
                case EventType.WeatherEvent:
                    return $"{intensity} weather event impacts the city";
                case EventType.CulturalEvent:
                    return $"{intensity} cultural event brings the community together";
                case EventType.TechDiscovery:
                    return $"{intensity} technological discovery advances progress";
                case EventType.SocialTrend:
                    return $"{intensity} social trend spreads through the population";
                case EventType.EconomicCrisis:
                    return "Economic crisis hits the city hard!";
                case EventType.NaturalDisaster:
                    return "Natural disaster causes widespread damage!";
                case EventType.PoliticalUpheaval:
                    return "Political upheaval creates instability!";
                case EventType.TechnologicalBreakthrough:
                    return "Major technological breakthrough revolutionizes the city!";
                case EventType.CulturalRevolution:
                    return "Cultural revolution transforms society!";
                default:
                    return "Something interesting happened in the city";
            }
        }

        /// <summary>
        /// Gets the event history
        /// </summary>
        public List<GameEvent> GetEventHistory()
        {
            return new List<GameEvent>(eventHistory);
        }
    }

    public enum EventType
    {
        MarketFluctuation,
        WeatherEvent,
        CulturalEvent,
        TechDiscovery,
        SocialTrend,
        EconomicCrisis,
        NaturalDisaster,
        PoliticalUpheaval,
        TechnologicalBreakthrough,
        CulturalRevolution
    }

    [global::System.Serializable]
    public struct GameEvent
    {
        public EventType eventType;
        public bool isMajor;
        public int day;
        public string description;
    }
}
