using UnityEngine;
using UnityEngine.SceneManagement;
using RichBillionaire.Manager;
using RichBillionaire.Pawns;
using RichBillionaire.Zones;

namespace RichBillionaire.System
{
    /// <summary>
    /// Central game manager that coordinates all systems and handles game state
    /// Manages initialization, game flow, save/load, and system communication
    /// </summary>
    public class GameManager : MonoBehaviour
    {
        [Header("Game Settings")]
        [SerializeField] private bool autoStartGame = true;
        [SerializeField] private int initialPawnCount = 20;
        [SerializeField] private float gameStartDelay = 1f;

        [Header("Game State")]
        public GameState currentState = GameState.Initializing;
        public float gameTime = 0f;
        public int gameDay = 1;
        public float dayLength = 60f; // 60 seconds = 1 game day

        [Header("Performance Settings")]
        [SerializeField] private int targetFrameRate = 60;
        [SerializeField] private bool enableVSync = true;

        // System References
        private PlayerResourceTracker resourceTracker;
        private TaxAndIncomeSystem taxSystem;
        private PawnGenerator pawnGenerator;
        private SimpleUI simpleUI;
        private EventSystem eventSystem;

        // Game state tracking
        private float lastDayTime = 0f;
        private bool systemsInitialized = false;

        // Events
        public global::System.Action<GameState> OnGameStateChanged;
        public global::System.Action<int> OnNewDay;
        public global::System.Action OnGameStarted;
        public global::System.Action OnGamePaused;
        public global::System.Action OnGameResumed;

        // Singleton pattern
        private static GameManager instance;
        public static GameManager Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = FindObjectOfType<GameManager>();
                    if (instance == null)
                    {
                        GameObject go = new GameObject("GameManager");
                        instance = go.AddComponent<GameManager>();
                    }
                }
                return instance;
            }
        }

        void Awake()
        {
            // Singleton setup
            if (instance == null)
            {
                instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeGameSettings();
            }
            else if (instance != this)
            {
                Destroy(gameObject);
                return;
            }
        }

        void Start()
        {
            if (autoStartGame)
            {
                Invoke(nameof(StartGame), gameStartDelay);
            }
        }

        void Update()
        {
            if (currentState == GameState.Playing)
            {
                UpdateGameTime();
                CheckForNewDay();
            }
        }

        /// <summary>
        /// Initializes basic game settings
        /// </summary>
        private void InitializeGameSettings()
        {
            // Set frame rate and VSync
            Application.targetFrameRate = targetFrameRate;
            QualitySettings.vSyncCount = enableVSync ? 1 : 0;

            // Set initial game state
            ChangeGameState(GameState.Initializing);
        }

        /// <summary>
        /// Starts the game and initializes all systems
        /// </summary>
        public void StartGame()
        {
            Debug.Log("Starting Rich Billionaire game...");

            ChangeGameState(GameState.Loading);

            // Initialize all systems
            InitializeSystems();

            // Generate initial population
            GenerateInitialPopulation();

            // Start the game
            ChangeGameState(GameState.Playing);
            gameTime = 0f;
            gameDay = 1;
            lastDayTime = Time.time;

            OnGameStarted?.Invoke();
            Debug.Log("Game started successfully!");
        }

        /// <summary>
        /// Initializes all game systems
        /// </summary>
        private void InitializeSystems()
        {
            // Find or create system components
            resourceTracker = FindOrCreateComponent<PlayerResourceTracker>();
            taxSystem = FindOrCreateComponent<TaxAndIncomeSystem>();
            pawnGenerator = FindOrCreateComponent<PawnGenerator>();
            simpleUI = FindOrCreateComponent<SimpleUI>();
            eventSystem = FindOrCreateComponent<EventSystem>();

            // Subscribe to system events
            SubscribeToSystemEvents();

            systemsInitialized = true;
            Debug.Log("All systems initialized");
        }

        /// <summary>
        /// Finds an existing component or creates a new one
        /// </summary>
        private T FindOrCreateComponent<T>() where T : Component
        {
            T component = FindObjectOfType<T>();
            if (component == null)
            {
                GameObject go = new GameObject(typeof(T).Name);
                component = go.AddComponent<T>();
            }
            return component;
        }

        /// <summary>
        /// Subscribes to events from various systems
        /// </summary>
        private void SubscribeToSystemEvents()
        {
            if (resourceTracker != null)
            {
                resourceTracker.OnResourceAlert += HandleResourceAlert;
            }

            if (taxSystem != null)
            {
                taxSystem.OnPolicyChanged += HandlePolicyChange;
            }

            if (pawnGenerator != null)
            {
                pawnGenerator.OnPawnsGenerated += HandlePawnsGenerated;
            }
        }

        /// <summary>
        /// Generates the initial population of pawns
        /// </summary>
        private void GenerateInitialPopulation()
        {
            if (pawnGenerator != null)
            {
                pawnGenerator.GenerateStartingPawns(initialPawnCount);
            }
        }

        /// <summary>
        /// Updates game time
        /// </summary>
        private void UpdateGameTime()
        {
            gameTime += Time.deltaTime;
        }

        /// <summary>
        /// Checks if a new day has started
        /// </summary>
        private void CheckForNewDay()
        {
            if (Time.time - lastDayTime >= dayLength)
            {
                gameDay++;
                lastDayTime = Time.time;
                OnNewDay?.Invoke(gameDay);

                // Trigger daily events
                TriggerDailyEvents();

                Debug.Log($"Day {gameDay} started");
            }
        }

        /// <summary>
        /// Triggers events that happen daily
        /// </summary>
        private void TriggerDailyEvents()
        {
            // Random events, population changes, etc.
            if (eventSystem != null)
            {
                eventSystem.TriggerDailyEvents();
            }

            // Population growth/decline
            if (Random.value < 0.1f) // 10% chance per day
            {
                TriggerPopulationEvent();
            }
        }

        /// <summary>
        /// Triggers population-related events
        /// </summary>
        private void TriggerPopulationEvent()
        {
            if (pawnGenerator == null) return;

            var pawns = pawnGenerator.GetAllPawns();
            float avgSatisfaction = 0f;

            foreach (var pawn in pawns)
            {
                avgSatisfaction += pawn.overallSatisfaction;
            }
            avgSatisfaction /= pawns.Count;

            // Population growth if people are happy
            if (avgSatisfaction > 70f && Random.value < 0.3f)
            {
                pawnGenerator.GenerateSinglePawn();
                Debug.Log("New citizen moved to the city!");
            }
            // Population decline if people are unhappy
            else if (avgSatisfaction < 30f && Random.value < 0.2f && pawns.Count > 5)
            {
                var unhappyPawn = pawns.Find(p => p.overallSatisfaction < 30f);
                if (unhappyPawn != null)
                {
                    pawnGenerator.RemovePawn(unhappyPawn);
                    Debug.Log($"{unhappyPawn.pawnName} left the city due to unhappiness");
                }
            }
        }

        /// <summary>
        /// Changes the game state
        /// </summary>
        public void ChangeGameState(GameState newState)
        {
            if (currentState == newState) return;

            GameState previousState = currentState;
            currentState = newState;

            Debug.Log($"Game state changed from {previousState} to {newState}");
            OnGameStateChanged?.Invoke(newState);

            // Handle state-specific logic
            switch (newState)
            {
                case GameState.Paused:
                    Time.timeScale = 0f;
                    OnGamePaused?.Invoke();
                    break;
                case GameState.Playing:
                    if (previousState == GameState.Paused)
                    {
                        Time.timeScale = 1f;
                        OnGameResumed?.Invoke();
                    }
                    break;
                case GameState.GameOver:
                    HandleGameOver();
                    break;
            }
        }

        /// <summary>
        /// Pauses or resumes the game
        /// </summary>
        public void TogglePause()
        {
            if (currentState == GameState.Playing)
            {
                ChangeGameState(GameState.Paused);
            }
            else if (currentState == GameState.Paused)
            {
                ChangeGameState(GameState.Playing);
            }
        }

        /// <summary>
        /// Handles game over conditions
        /// </summary>
        private void HandleGameOver()
        {
            Time.timeScale = 0f;
            Debug.Log("Game Over!");

            // Show game over screen, calculate final score, etc.
            // This would be expanded in a full implementation
        }

        /// <summary>
        /// Restarts the current game
        /// </summary>
        public void RestartGame()
        {
            Time.timeScale = 1f;
            SceneManager.LoadScene(SceneManager.GetActiveScene().name);
        }

        /// <summary>
        /// Quits the game
        /// </summary>
        public void QuitGame()
        {
            Debug.Log("Quitting game...");
            Application.Quit();
        }

        /// <summary>
        /// Gets game statistics
        /// </summary>
        public GameStats GetGameStats()
        {
            var stats = new GameStats
            {
                gameDay = this.gameDay,
                gameTime = this.gameTime,
                totalPawns = pawnGenerator?.GetAllPawns().Count ?? 0,
                totalMoney = resourceTracker?.money ?? 0f,
                avgSatisfaction = 0f
            };

            // Calculate average satisfaction
            if (pawnGenerator != null)
            {
                var pawns = pawnGenerator.GetAllPawns();
                if (pawns.Count > 0)
                {
                    float totalSatisfaction = 0f;
                    foreach (var pawn in pawns)
                    {
                        totalSatisfaction += pawn.overallSatisfaction;
                    }
                    stats.avgSatisfaction = totalSatisfaction / pawns.Count;
                }
            }

            return stats;
        }

        // Event handlers
        private void HandleResourceAlert(string alert)
        {
            Debug.LogWarning($"Resource Alert: {alert}");
        }

        private void HandlePolicyChange(string change)
        {
            Debug.Log($"Policy Change: {change}");
        }

        private void HandlePawnsGenerated(global::System.Collections.Generic.List<Pawn> pawns)
        {
            Debug.Log($"Generated {pawns.Count} pawns");
        }

        void OnDestroy()
        {
            // Unsubscribe from events
            if (resourceTracker != null)
            {
                resourceTracker.OnResourceAlert -= HandleResourceAlert;
            }

            if (taxSystem != null)
            {
                taxSystem.OnPolicyChanged -= HandlePolicyChange;
            }

            if (pawnGenerator != null)
            {
                pawnGenerator.OnPawnsGenerated -= HandlePawnsGenerated;
            }
        }
    }

    public enum GameState
    {
        Initializing,
        Loading,
        Playing,
        Paused,
        GameOver,
        Menu
    }

    [global::System.Serializable]
    public struct GameStats
    {
        public int gameDay;
        public float gameTime;
        public int totalPawns;
        public float totalMoney;
        public float avgSatisfaction;
    }
}
