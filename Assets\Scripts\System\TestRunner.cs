using UnityEngine;
using RichBillionaire.Pawns;
using RichBillionaire.Manager;
using RichBillionaire.Zones;

namespace RichBillionaire.System
{
    /// <summary>
    /// Test runner for validating game systems and demonstrating functionality
    /// Useful for debugging and ensuring all systems work correctly
    /// </summary>
    public class TestRunner : MonoBehaviour
    {
        [Header("Test Settings")]
        [SerializeField] private bool runTestsOnStart = false;
        [SerializeField] private bool enableDebugLogs = true;
        [SerializeField] private float testInterval = 5f;

        [Header("Test Controls")]
        [SerializeField] private KeyCode runAllTestsKey = KeyCode.T;
        [SerializeField] private KeyCode generatePawnKey = KeyCode.P;
        [SerializeField] private KeyCode addResourcesKey = KeyCode.R;
        [SerializeField] private KeyCode triggerEventKey = KeyCode.E;

        // System references
        private PawnGenerator pawnGenerator;
        private PlayerResourceTracker resourceTracker;
        private TaxAndIncomeSystem taxSystem;
        private EventSystem eventSystem;

        // Test tracking
        private float lastTestTime;
        private int testsPassed = 0;
        private int testsFailed = 0;

        void Start()
        {
            InitializeReferences();
            
            if (runTestsOnStart)
            {
                Invoke(nameof(RunAllTests), 2f); // Wait for systems to initialize
            }
        }

        void Update()
        {
            HandleTestInputs();
            
            // Run periodic tests
            if (Time.time - lastTestTime >= testInterval && enableDebugLogs)
            {
                RunPeriodicTests();
                lastTestTime = Time.time;
            }
        }

        /// <summary>
        /// Initializes references to game systems
        /// </summary>
        private void InitializeReferences()
        {
            pawnGenerator = FindObjectOfType<PawnGenerator>();
            resourceTracker = FindObjectOfType<PlayerResourceTracker>();
            taxSystem = FindObjectOfType<TaxAndIncomeSystem>();
            eventSystem = FindObjectOfType<EventSystem>();
        }

        /// <summary>
        /// Handles keyboard inputs for testing
        /// </summary>
        private void HandleTestInputs()
        {
            if (Input.GetKeyDown(runAllTestsKey))
            {
                RunAllTests();
            }
            
            if (Input.GetKeyDown(generatePawnKey))
            {
                TestPawnGeneration();
            }
            
            if (Input.GetKeyDown(addResourcesKey))
            {
                TestResourceManagement();
            }
            
            if (Input.GetKeyDown(triggerEventKey))
            {
                TestEventSystem();
            }
        }

        /// <summary>
        /// Runs all available tests
        /// </summary>
        public void RunAllTests()
        {
            Log("=== RUNNING ALL TESTS ===");
            testsPassed = 0;
            testsFailed = 0;

            TestPawnGeneration();
            TestPawnStats();
            TestResourceManagement();
            TestZoneSystem();
            TestTaxSystem();
            TestEventSystem();
            TestNameDatabase();

            Log($"=== TEST RESULTS: {testsPassed} PASSED, {testsFailed} FAILED ===");
        }

        /// <summary>
        /// Tests pawn generation system
        /// </summary>
        public void TestPawnGeneration()
        {
            Log("Testing Pawn Generation...");
            
            if (pawnGenerator == null)
            {
                LogError("PawnGenerator not found!");
                return;
            }

            int initialCount = pawnGenerator.GetAllPawns().Count;
            Pawn newPawn = pawnGenerator.GenerateSinglePawn();
            
            if (newPawn != null)
            {
                LogSuccess($"Generated pawn: {newPawn.pawnName}");
                TestPassed();
            }
            else
            {
                LogError("Failed to generate pawn");
                TestFailed();
            }

            int finalCount = pawnGenerator.GetAllPawns().Count;
            if (finalCount == initialCount + 1)
            {
                LogSuccess("Pawn count increased correctly");
                TestPassed();
            }
            else
            {
                LogError($"Pawn count mismatch: expected {initialCount + 1}, got {finalCount}");
                TestFailed();
            }
        }

        /// <summary>
        /// Tests pawn stats system
        /// </summary>
        public void TestPawnStats()
        {
            Log("Testing Pawn Stats...");
            
            if (pawnGenerator == null)
            {
                LogError("PawnGenerator not found!");
                return;
            }

            var pawns = pawnGenerator.GetAllPawns();
            if (pawns.Count == 0)
            {
                LogError("No pawns available for testing");
                TestFailed();
                return;
            }

            Pawn testPawn = pawns[0];
            
            // Test stat ranges
            bool statsValid = true;
            if (testPawn.stats.intellect < -100 || testPawn.stats.intellect > 100) statsValid = false;
            if (testPawn.stats.morality < -100 || testPawn.stats.morality > 100) statsValid = false;
            if (testPawn.stats.ambition < -100 || testPawn.stats.ambition > 100) statsValid = false;

            if (statsValid)
            {
                LogSuccess("Pawn stats are within valid ranges");
                TestPassed();
            }
            else
            {
                LogError("Pawn stats are outside valid ranges");
                TestFailed();
            }

            // Test zone effectiveness calculation
            float effectiveness = testPawn.stats.GetZoneEffectiveness(ZoneType.Industrial);
            if (effectiveness > 0)
            {
                LogSuccess($"Zone effectiveness calculated: {effectiveness:F2}");
                TestPassed();
            }
            else
            {
                LogError("Zone effectiveness calculation failed");
                TestFailed();
            }
        }

        /// <summary>
        /// Tests resource management system
        /// </summary>
        public void TestResourceManagement()
        {
            Log("Testing Resource Management...");
            
            if (resourceTracker == null)
            {
                LogError("PlayerResourceTracker not found!");
                return;
            }

            float initialMoney = resourceTracker.money;
            
            // Test adding resources
            bool addSuccess = resourceTracker.AddResource(ResourceType.Money, 100f);
            if (addSuccess && resourceTracker.money == initialMoney + 100f)
            {
                LogSuccess("Successfully added money");
                TestPassed();
            }
            else
            {
                LogError("Failed to add money");
                TestFailed();
            }

            // Test spending resources
            bool spendSuccess = resourceTracker.SpendResource(ResourceType.Money, 50f);
            if (spendSuccess && resourceTracker.money == initialMoney + 50f)
            {
                LogSuccess("Successfully spent money");
                TestPassed();
            }
            else
            {
                LogError("Failed to spend money");
                TestFailed();
            }

            // Test resource checking
            bool hasEnough = resourceTracker.HasResource(ResourceType.Money, 10f);
            if (hasEnough)
            {
                LogSuccess("Resource checking works correctly");
                TestPassed();
            }
            else
            {
                LogError("Resource checking failed");
                TestFailed();
            }
        }

        /// <summary>
        /// Tests zone system
        /// </summary>
        public void TestZoneSystem()
        {
            Log("Testing Zone System...");
            
            // Create a test zone
            GameObject zoneObj = new GameObject("Test Zone");
            Zone testZone = zoneObj.AddComponent<Zone>();
            testZone.zoneType = ZoneType.Industrial;
            testZone.zoneName = "Test Industrial Zone";

            if (testZone != null)
            {
                LogSuccess("Created test zone");
                TestPassed();
            }
            else
            {
                LogError("Failed to create test zone");
                TestFailed();
                return;
            }

            // Test adding worker
            if (pawnGenerator != null)
            {
                var pawns = pawnGenerator.GetAllPawns();
                if (pawns.Count > 0)
                {
                    bool addWorkerSuccess = testZone.AddWorker(pawns[0]);
                    if (addWorkerSuccess)
                    {
                        LogSuccess("Successfully added worker to zone");
                        TestPassed();
                    }
                    else
                    {
                        LogError("Failed to add worker to zone");
                        TestFailed();
                    }
                }
            }

            // Clean up
            DestroyImmediate(zoneObj);
        }

        /// <summary>
        /// Tests tax system
        /// </summary>
        public void TestTaxSystem()
        {
            Log("Testing Tax System...");
            
            if (taxSystem == null)
            {
                LogError("TaxAndIncomeSystem not found!");
                return;
            }

            float initialTaxRate = taxSystem.incomeTaxRate;
            
            // Test tax rate change
            taxSystem.SetTaxRate("income", 20f);
            if (taxSystem.incomeTaxRate == 20f)
            {
                LogSuccess("Tax rate changed successfully");
                TestPassed();
            }
            else
            {
                LogError("Failed to change tax rate");
                TestFailed();
            }

            // Test policy toggle
            bool initialUBI = taxSystem.universalBasicIncome;
            taxSystem.TogglePolicy("ubi");
            if (taxSystem.universalBasicIncome != initialUBI)
            {
                LogSuccess("Policy toggled successfully");
                TestPassed();
            }
            else
            {
                LogError("Failed to toggle policy");
                TestFailed();
            }

            // Restore original values
            taxSystem.SetTaxRate("income", initialTaxRate);
            if (taxSystem.universalBasicIncome != initialUBI)
            {
                taxSystem.TogglePolicy("ubi");
            }
        }

        /// <summary>
        /// Tests event system
        /// </summary>
        public void TestEventSystem()
        {
            Log("Testing Event System...");
            
            if (eventSystem == null)
            {
                LogError("EventSystem not found!");
                return;
            }

            int initialEventCount = eventSystem.GetEventHistory().Count;
            
            // Trigger daily events
            eventSystem.TriggerDailyEvents();
            
            // Check if events were potentially triggered (they're random)
            LogSuccess("Event system trigger completed (events are random)");
            TestPassed();
        }

        /// <summary>
        /// Tests name database
        /// </summary>
        public void TestNameDatabase()
        {
            Log("Testing Name Database...");
            
            var nameDB = PawnNameDatabase.Instance;
            if (nameDB == null)
            {
                LogError("PawnNameDatabase not found!");
                return;
            }

            // Test random ethnicity generation
            string ethnicity = nameDB.GetRandomEthnicity();
            if (!string.IsNullOrEmpty(ethnicity))
            {
                LogSuccess($"Generated random ethnicity: {ethnicity}");
                TestPassed();
            }
            else
            {
                LogError("Failed to generate random ethnicity");
                TestFailed();
            }

            // Test name generation
            string name = nameDB.GetRandomName(Gender.Male, "White");
            if (!string.IsNullOrEmpty(name) && name != "Unknown Person")
            {
                LogSuccess($"Generated random name: {name}");
                TestPassed();
            }
            else
            {
                LogError("Failed to generate random name");
                TestFailed();
            }
        }

        /// <summary>
        /// Runs periodic tests to monitor system health
        /// </summary>
        private void RunPeriodicTests()
        {
            // Check system health
            if (pawnGenerator != null)
            {
                var pawns = pawnGenerator.GetAllPawns();
                Log($"Population: {pawns.Count} pawns");
            }

            if (resourceTracker != null)
            {
                Log($"Money: ${resourceTracker.money:F0}");
            }
        }

        // Logging methods
        private void Log(string message)
        {
            if (enableDebugLogs)
                Debug.Log($"[TEST] {message}");
        }

        private void LogSuccess(string message)
        {
            if (enableDebugLogs)
                Debug.Log($"[TEST SUCCESS] {message}");
        }

        private void LogError(string message)
        {
            if (enableDebugLogs)
                Debug.LogError($"[TEST ERROR] {message}");
        }

        private void TestPassed()
        {
            testsPassed++;
        }

        private void TestFailed()
        {
            testsFailed++;
        }

        /// <summary>
        /// Gets test statistics
        /// </summary>
        public string GetTestStats()
        {
            return $"Tests Passed: {testsPassed}, Tests Failed: {testsFailed}";
        }
    }
}
