using UnityEngine;
using RichBillionaire.Manager;
using RichBillionaire.Pawns;
using RichBillionaire.Zones;

namespace RichBillionaire.UI
{
    /// <summary>
    /// Main dashboard UI that displays player resources, current pawn info, and game controls
    /// Central hub for all player interactions and information display
    /// </summary>
    public class DashboardUI : MonoBehaviour
    {
        [Header("Dashboard Settings")]
        [SerializeField] private bool enableDebugOutput = true;
        [SerializeField] private KeyCode previousPawnKey = KeyCode.LeftArrow;
        [SerializeField] private KeyCode nextPawnKey = KeyCode.RightArrow;
        [SerializeField] private KeyCode pauseKey = KeyCode.Space;
        [SerializeField] private KeyCode speedUpKey = KeyCode.Plus;
        [SerializeField] private KeyCode normalSpeedKey = KeyCode.Minus;

        // References
        private PlayerResourceTracker resourceTracker;
        private PawnGenerator pawnGenerator;
        private TaxAndIncomeSystem taxSystem;
        private PawnNavigationUI pawnNavigation;

        // Current state
        private Pawn currentPawn;
        private int currentPawnIndex = 0;
        private float gameSpeed = 1f;
        private bool isPaused = false;

        // Update intervals
        private float lastUIUpdate = 0f;
        private float uiUpdateInterval = 0.5f; // Update UI twice per second

        void Start()
        {
            InitializeReferences();
            SetupButtonListeners();
            InitializeUI();
        }

        void Update()
        {
            HandleKeyboardInput();

            // Update display periodically
            if (Time.time - lastUIUpdate >= uiUpdateInterval)
            {
                UpdateDisplay();
                lastUIUpdate = Time.time;
            }
        }

        /// <summary>
        /// Handles keyboard input for dashboard controls
        /// </summary>
        private void HandleKeyboardInput()
        {
            if (Input.GetKeyDown(previousPawnKey))
            {
                ShowPreviousPawn();
            }

            if (Input.GetKeyDown(nextPawnKey))
            {
                ShowNextPawn();
            }

            if (Input.GetKeyDown(pauseKey))
            {
                TogglePause();
            }

            if (Input.GetKeyDown(speedUpKey))
            {
                SpeedUpGame();
            }

            if (Input.GetKeyDown(normalSpeedKey))
            {
                NormalSpeed();
            }
        }

        /// <summary>
        /// Initializes references to other systems
        /// </summary>
        private void InitializeReferences()
        {
            resourceTracker = FindObjectOfType<PlayerResourceTracker>();
            pawnGenerator = FindObjectOfType<PawnGenerator>();
            taxSystem = FindObjectOfType<TaxAndIncomeSystem>();
            pawnNavigation = GetComponent<PawnNavigationUI>();

            if (pawnNavigation == null)
            {
                pawnNavigation = gameObject.AddComponent<PawnNavigationUI>();
            }
        }

        /// <summary>
        /// Sets up button click listeners (simplified for non-UI version)
        /// </summary>
        private void SetupButtonListeners()
        {
            // In a full UI implementation, this would set up button click listeners
            // For now, we use keyboard input instead
            Debug.Log("Dashboard UI initialized - Use arrow keys to navigate pawns, Space to pause");
        }

        /// <summary>
        /// Initializes UI with default values
        /// </summary>
        private void InitializeUI()
        {
            // Set initial game speed
            Time.timeScale = gameSpeed;

            // Show first pawn if available
            if (pawnGenerator != null)
            {
                var pawns = pawnGenerator.GetAllPawns();
                if (pawns.Count > 0)
                {
                    currentPawn = pawns[0];
                    currentPawnIndex = 0;
                    if (enableDebugOutput)
                        Debug.Log($"Selected initial pawn: {currentPawn.pawnName}");
                }
            }
        }

        /// <summary>
        /// Updates the display (simplified debug version)
        /// </summary>
        private void UpdateDisplay()
        {
            if (!enableDebugOutput) return;

            // Display current pawn info
            if (currentPawn != null)
            {
                string pawnInfo = $"Current Pawn: {currentPawn.pawnName} | ";
                pawnInfo += $"Satisfaction: {currentPawn.overallSatisfaction:F0}% | ";
                pawnInfo += $"Income: ${currentPawn.dailyIncome:F0}/day";

                // Only log every 10 seconds to avoid spam
                if (Time.time % 10f < uiUpdateInterval)
                {
                    Debug.Log(pawnInfo);
                }
            }

            // Display resource info
            if (resourceTracker != null && Time.time % 15f < uiUpdateInterval)
            {
                string resourceInfo = $"Resources: Money=${resourceTracker.money:F0} | ";
                resourceInfo += $"Materials={resourceTracker.materials:F0} | ";
                resourceInfo += $"Food={resourceTracker.food:F0}";
                Debug.Log(resourceInfo);
            }
        }



        /// <summary>
        /// Shows the previous pawn in the list
        /// </summary>
        public void ShowPreviousPawn()
        {
            if (pawnGenerator == null) return;

            var pawns = pawnGenerator.GetAllPawns();
            if (pawns.Count == 0) return;

            currentPawnIndex = (currentPawnIndex - 1 + pawns.Count) % pawns.Count;
            currentPawn = pawns[currentPawnIndex];
        }

        /// <summary>
        /// Shows the next pawn in the list
        /// </summary>
        public void ShowNextPawn()
        {
            if (pawnGenerator == null) return;

            var pawns = pawnGenerator.GetAllPawns();
            if (pawns.Count == 0) return;

            currentPawnIndex = (currentPawnIndex + 1) % pawns.Count;
            currentPawn = pawns[currentPawnIndex];
        }

        /// <summary>
        /// Toggles game pause
        /// </summary>
        public void TogglePause()
        {
            isPaused = !isPaused;
            Time.timeScale = isPaused ? 0f : gameSpeed;
            UpdateGameSpeedDisplay();
        }

        /// <summary>
        /// Speeds up the game
        /// </summary>
        public void SpeedUpGame()
        {
            gameSpeed = Mathf.Min(gameSpeed * 2f, 8f);
            if (!isPaused) Time.timeScale = gameSpeed;
            UpdateGameSpeedDisplay();
        }

        /// <summary>
        /// Returns game to normal speed
        /// </summary>
        public void NormalSpeed()
        {
            gameSpeed = 1f;
            if (!isPaused) Time.timeScale = gameSpeed;
            UpdateGameSpeedDisplay();
        }

        /// <summary>
        /// Updates game speed display (debug version)
        /// </summary>
        private void UpdateGameSpeedDisplay()
        {
            if (enableDebugOutput)
            {
                if (isPaused)
                    Debug.Log("Game PAUSED");
                else
                    Debug.Log($"Game Speed: {gameSpeed:F0}x");
            }
        }

        /// <summary>
        /// Satisfies current pawn's basic needs (costs money)
        /// </summary>
        public void SatisfyCurrentPawnBasicNeeds()
        {
            if (currentPawn == null || resourceTracker == null) return;

            float cost = 100f;
            if (resourceTracker.HasResource(ResourceType.Money, cost))
            {
                resourceTracker.SpendResource(ResourceType.Money, cost);
                currentPawn.SatisfyBasicNeeds(30f);
                Debug.Log($"Satisfied {currentPawn.pawnName}'s basic needs for ${cost}");
            }
        }

        /// <summary>
        /// Satisfies current pawn's luxury needs (costs money)
        /// </summary>
        public void SatisfyCurrentPawnLuxuryNeeds()
        {
            if (currentPawn == null || resourceTracker == null) return;

            float cost = 200f;
            if (resourceTracker.HasResource(ResourceType.Money, cost))
            {
                resourceTracker.SpendResource(ResourceType.Money, cost);
                currentPawn.SatisfyLuxuryNeeds(25f);
                Debug.Log($"Satisfied {currentPawn.pawnName}'s luxury needs for ${cost}");
            }
        }

        /// <summary>
        /// Reassigns current pawn to a random new job
        /// </summary>
        public void ReassignCurrentPawnJob()
        {
            if (currentPawn == null) return;

            ZoneType[] zoneTypes = global::System.Enum.GetValues(typeof(ZoneType)) as ZoneType[];
            ZoneType newZone = zoneTypes[Random.Range(0, zoneTypes.Length)];
            currentPawn.AssignToZone(newZone);
            Debug.Log($"Reassigned {currentPawn.pawnName} to work in {newZone}");
        }
    }
}
