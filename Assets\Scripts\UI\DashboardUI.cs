using UnityEngine;
using UnityEngine.UI;
using TMPro;
using RichBillionaire.Manager;
using RichBillionaire.Pawns;
using RichBillionaire.Zones;

namespace RichBillionaire.UI
{
    /// <summary>
    /// Main dashboard UI that displays player resources, current pawn info, and game controls
    /// Central hub for all player interactions and information display
    /// </summary>
    public class DashboardUI : MonoBehaviour
    {
        [Header("Resource Display")]
        [SerializeField] private TextMeshProUGUI moneyText;
        [SerializeField] private TextMeshProUGUI materialsText;
        [SerializeField] private TextMeshProUGUI foodText;
        [SerializeField] private TextMeshProUGUI cultureText;
        [SerializeField] private TextMeshP<PERSON><PERSON>GUI happinessText;
        [SerializeField] private TextMesh<PERSON><PERSON>UGUI influenceText;

        [Header("Current Pawn Display")]
        [SerializeField] private Image pawnPortrait;
        [SerializeField] private TextMeshP<PERSON><PERSON><PERSON><PERSON> pawnNameText;
        [SerializeField] private TextMeshProUGUI pawnAgeGenderText;
        [SerializeField] private TextMeshP<PERSON><PERSON><PERSON><PERSON> pawnJobText;
        [SerializeField] private TextMesh<PERSON><PERSON><PERSON><PERSON><PERSON> pawnIncomeText;

        [Header("Pawn Stats Bars")]
        [SerializeField] private Slider basicNeedsSlider;
        [SerializeField] private Slider luxuryNeedsSlider;
        [SerializeField] private Slider socialNeedsSlider;
        [SerializeField] private Slider satisfactionSlider;
        [SerializeField] private TextMeshProUGUI basicNeedsText;
        [SerializeField] private TextMeshProUGUI luxuryNeedsText;
        [SerializeField] private TextMeshProUGUI socialNeedsText;
        [SerializeField] private TextMeshProUGUI satisfactionText;

        [Header("Pawn Navigation")]
        [SerializeField] private Button previousPawnButton;
        [SerializeField] private Button nextPawnButton;
        [SerializeField] private TextMeshProUGUI pawnCounterText;

        [Header("Game Controls")]
        [SerializeField] private Button pauseButton;
        [SerializeField] private Button speedUpButton;
        [SerializeField] private Button normalSpeedButton;
        [SerializeField] private TextMeshProUGUI gameSpeedText;

        [Header("Information Panels")]
        [SerializeField] private TextMeshProUGUI populationStatsText;
        [SerializeField] private TextMeshProUGUI economicStatsText;
        [SerializeField] private TextMeshProUGUI alertsText;

        [Header("Action Buttons")]
        [SerializeField] private Button satisfyBasicNeedsButton;
        [SerializeField] private Button satisfyLuxuryNeedsButton;
        [SerializeField] private Button reassignJobButton;

        // References
        private PlayerResourceTracker resourceTracker;
        private PawnGenerator pawnGenerator;
        private TaxAndIncomeSystem taxSystem;
        private PawnNavigationUI pawnNavigation;

        // Current state
        private Pawn currentPawn;
        private int currentPawnIndex = 0;
        private float gameSpeed = 1f;
        private bool isPaused = false;

        // Update intervals
        private float lastUIUpdate = 0f;
        private float uiUpdateInterval = 0.5f; // Update UI twice per second

        void Start()
        {
            InitializeReferences();
            SetupButtonListeners();
            InitializeUI();
        }

        void Update()
        {
            // Update UI periodically
            if (Time.time - lastUIUpdate >= uiUpdateInterval)
            {
                UpdateResourceDisplay();
                UpdatePawnDisplay();
                UpdateInformationPanels();
                lastUIUpdate = Time.time;
            }
        }

        /// <summary>
        /// Initializes references to other systems
        /// </summary>
        private void InitializeReferences()
        {
            resourceTracker = FindObjectOfType<PlayerResourceTracker>();
            pawnGenerator = FindObjectOfType<PawnGenerator>();
            taxSystem = FindObjectOfType<TaxAndIncomeSystem>();
            pawnNavigation = GetComponent<PawnNavigationUI>();

            if (pawnNavigation == null)
            {
                pawnNavigation = gameObject.AddComponent<PawnNavigationUI>();
            }
        }

        /// <summary>
        /// Sets up button click listeners
        /// </summary>
        private void SetupButtonListeners()
        {
            if (previousPawnButton != null)
                previousPawnButton.onClick.AddListener(ShowPreviousPawn);
            
            if (nextPawnButton != null)
                nextPawnButton.onClick.AddListener(ShowNextPawn);
            
            if (pauseButton != null)
                pauseButton.onClick.AddListener(TogglePause);
            
            if (speedUpButton != null)
                speedUpButton.onClick.AddListener(SpeedUpGame);
            
            if (normalSpeedButton != null)
                normalSpeedButton.onClick.AddListener(NormalSpeed);
            
            if (satisfyBasicNeedsButton != null)
                satisfyBasicNeedsButton.onClick.AddListener(SatisfyCurrentPawnBasicNeeds);
            
            if (satisfyLuxuryNeedsButton != null)
                satisfyLuxuryNeedsButton.onClick.AddListener(SatisfyCurrentPawnLuxuryNeeds);
            
            if (reassignJobButton != null)
                reassignJobButton.onClick.AddListener(ReassignCurrentPawnJob);
        }

        /// <summary>
        /// Initializes UI with default values
        /// </summary>
        private void InitializeUI()
        {
            // Set initial game speed
            Time.timeScale = gameSpeed;
            UpdateGameSpeedDisplay();

            // Show first pawn if available
            if (pawnGenerator != null)
            {
                var pawns = pawnGenerator.GetAllPawns();
                if (pawns.Count > 0)
                {
                    currentPawn = pawns[0];
                    currentPawnIndex = 0;
                }
            }

            // Initialize sliders
            if (basicNeedsSlider != null) basicNeedsSlider.maxValue = 100f;
            if (luxuryNeedsSlider != null) luxuryNeedsSlider.maxValue = 100f;
            if (socialNeedsSlider != null) socialNeedsSlider.maxValue = 100f;
            if (satisfactionSlider != null) satisfactionSlider.maxValue = 100f;
        }

        /// <summary>
        /// Updates resource display texts
        /// </summary>
        private void UpdateResourceDisplay()
        {
            if (resourceTracker == null) return;

            if (moneyText != null)
                moneyText.text = $"${resourceTracker.money:F0}";
            
            if (materialsText != null)
                materialsText.text = $"{resourceTracker.materials:F0}";
            
            if (foodText != null)
                foodText.text = $"{resourceTracker.food:F0}";
            
            if (cultureText != null)
                cultureText.text = $"{resourceTracker.culture:F0}";
            
            if (happinessText != null)
                happinessText.text = $"{resourceTracker.happiness:F0}";
            
            if (influenceText != null)
                influenceText.text = $"{resourceTracker.influence:F0}";
        }

        /// <summary>
        /// Updates current pawn display
        /// </summary>
        private void UpdatePawnDisplay()
        {
            if (currentPawn == null) return;

            // Basic info
            if (pawnNameText != null)
                pawnNameText.text = currentPawn.pawnName;
            
            if (pawnAgeGenderText != null)
                pawnAgeGenderText.text = $"{currentPawn.age}y {currentPawn.gender} {currentPawn.ethnicity}";
            
            if (pawnJobText != null)
                pawnJobText.text = $"Works in: {currentPawn.currentWorkZone}";
            
            if (pawnIncomeText != null)
                pawnIncomeText.text = $"Income: ${currentPawn.dailyIncome:F0}/day";

            // Needs bars
            if (basicNeedsSlider != null)
            {
                basicNeedsSlider.value = currentPawn.basicNeeds;
                if (basicNeedsText != null)
                    basicNeedsText.text = $"Basic Needs: {currentPawn.basicNeeds:F0}%";
            }
            
            if (luxuryNeedsSlider != null)
            {
                luxuryNeedsSlider.value = currentPawn.luxuryNeeds;
                if (luxuryNeedsText != null)
                    luxuryNeedsText.text = $"Luxury Needs: {currentPawn.luxuryNeeds:F0}%";
            }
            
            if (socialNeedsSlider != null)
            {
                socialNeedsSlider.value = currentPawn.socialNeeds;
                if (socialNeedsText != null)
                    socialNeedsText.text = $"Social Needs: {currentPawn.socialNeeds:F0}%";
            }
            
            if (satisfactionSlider != null)
            {
                satisfactionSlider.value = currentPawn.overallSatisfaction;
                if (satisfactionText != null)
                    satisfactionText.text = $"Satisfaction: {currentPawn.overallSatisfaction:F0}%";
            }

            // Portrait
            if (pawnPortrait != null)
            {
                SpriteRenderer spriteRenderer = currentPawn.GetComponent<SpriteRenderer>();
                if (spriteRenderer != null && spriteRenderer.sprite != null)
                {
                    pawnPortrait.sprite = spriteRenderer.sprite;
                }
            }

            // Pawn counter
            if (pawnCounterText != null && pawnGenerator != null)
            {
                var pawns = pawnGenerator.GetAllPawns();
                pawnCounterText.text = $"{currentPawnIndex + 1} / {pawns.Count}";
            }
        }

        /// <summary>
        /// Updates information panels
        /// </summary>
        private void UpdateInformationPanels()
        {
            // Population stats
            if (populationStatsText != null && pawnGenerator != null)
            {
                populationStatsText.text = pawnGenerator.GetPopulationStats();
            }

            // Economic stats
            if (economicStatsText != null && taxSystem != null)
            {
                economicStatsText.text = taxSystem.GetEconomicSummary();
            }

            // Alerts (placeholder for now)
            if (alertsText != null)
            {
                string alerts = "";
                if (resourceTracker != null)
                {
                    if (resourceTracker.money < 1000) alerts += "• Low on money!\n";
                    if (resourceTracker.food < 50) alerts += "• Food shortage!\n";
                    if (resourceTracker.happiness < 100) alerts += "• Population unhappy!\n";
                }
                alertsText.text = string.IsNullOrEmpty(alerts) ? "No alerts" : alerts;
            }
        }

        /// <summary>
        /// Shows the previous pawn in the list
        /// </summary>
        public void ShowPreviousPawn()
        {
            if (pawnGenerator == null) return;

            var pawns = pawnGenerator.GetAllPawns();
            if (pawns.Count == 0) return;

            currentPawnIndex = (currentPawnIndex - 1 + pawns.Count) % pawns.Count;
            currentPawn = pawns[currentPawnIndex];
        }

        /// <summary>
        /// Shows the next pawn in the list
        /// </summary>
        public void ShowNextPawn()
        {
            if (pawnGenerator == null) return;

            var pawns = pawnGenerator.GetAllPawns();
            if (pawns.Count == 0) return;

            currentPawnIndex = (currentPawnIndex + 1) % pawns.Count;
            currentPawn = pawns[currentPawnIndex];
        }

        /// <summary>
        /// Toggles game pause
        /// </summary>
        public void TogglePause()
        {
            isPaused = !isPaused;
            Time.timeScale = isPaused ? 0f : gameSpeed;
            UpdateGameSpeedDisplay();
        }

        /// <summary>
        /// Speeds up the game
        /// </summary>
        public void SpeedUpGame()
        {
            gameSpeed = Mathf.Min(gameSpeed * 2f, 8f);
            if (!isPaused) Time.timeScale = gameSpeed;
            UpdateGameSpeedDisplay();
        }

        /// <summary>
        /// Returns game to normal speed
        /// </summary>
        public void NormalSpeed()
        {
            gameSpeed = 1f;
            if (!isPaused) Time.timeScale = gameSpeed;
            UpdateGameSpeedDisplay();
        }

        /// <summary>
        /// Updates game speed display
        /// </summary>
        private void UpdateGameSpeedDisplay()
        {
            if (gameSpeedText != null)
            {
                if (isPaused)
                    gameSpeedText.text = "PAUSED";
                else
                    gameSpeedText.text = $"Speed: {gameSpeed:F0}x";
            }
        }

        /// <summary>
        /// Satisfies current pawn's basic needs (costs money)
        /// </summary>
        public void SatisfyCurrentPawnBasicNeeds()
        {
            if (currentPawn == null || resourceTracker == null) return;

            float cost = 100f;
            if (resourceTracker.HasResource(ResourceType.Money, cost))
            {
                resourceTracker.SpendResource(ResourceType.Money, cost);
                currentPawn.SatisfyBasicNeeds(30f);
                Debug.Log($"Satisfied {currentPawn.pawnName}'s basic needs for ${cost}");
            }
        }

        /// <summary>
        /// Satisfies current pawn's luxury needs (costs money)
        /// </summary>
        public void SatisfyCurrentPawnLuxuryNeeds()
        {
            if (currentPawn == null || resourceTracker == null) return;

            float cost = 200f;
            if (resourceTracker.HasResource(ResourceType.Money, cost))
            {
                resourceTracker.SpendResource(ResourceType.Money, cost);
                currentPawn.SatisfyLuxuryNeeds(25f);
                Debug.Log($"Satisfied {currentPawn.pawnName}'s luxury needs for ${cost}");
            }
        }

        /// <summary>
        /// Reassigns current pawn to a random new job
        /// </summary>
        public void ReassignCurrentPawnJob()
        {
            if (currentPawn == null) return;

            ZoneType[] zoneTypes = System.Enum.GetValues(typeof(ZoneType)) as ZoneType[];
            ZoneType newZone = zoneTypes[Random.Range(0, zoneTypes.Length)];
            currentPawn.AssignToZone(newZone);
            Debug.Log($"Reassigned {currentPawn.pawnName} to work in {newZone}");
        }
    }
}
