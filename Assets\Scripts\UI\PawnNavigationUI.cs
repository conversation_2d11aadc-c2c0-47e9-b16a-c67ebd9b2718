using UnityEngine;
using System.Collections.Generic;
using RichBillionaire.Pawns;

namespace RichBillionaire.UI
{
    /// <summary>
    /// Handles navigation between pawns with arrow buttons and search functionality
    /// Provides filtering and sorting options for the pawn list
    /// </summary>
    public class PawnNavigationUI : MonoBehaviour
    {
        [Header("Navigation Settings")]
        [SerializeField] private bool enableDebugOutput = true;

        // Navigation state
        private List<Pawn> allPawns = new List<Pawn>();
        private List<Pawn> filteredPawns = new List<Pawn>();
        private int currentIndex = 0;
        private string currentSearchTerm = "";
        private SortCriteria currentSort = SortCriteria.Name;
        private FilterCriteria currentFilter = FilterCriteria.All;

        // References
        private PawnGenerator pawnGenerator;

        // Events
        public global::System.Action<Pawn> OnPawnSelected;
        public global::System.Action<int, int> OnNavigationChanged; // currentIndex, totalCount

        void Start()
        {
            InitializeReferences();
            SetupButtonListeners();
            SetupDropdowns();
            RefreshPawnList();
        }

        void Update()
        {
            // Refresh pawn list periodically in case pawns are added/removed
            if (Time.frameCount % 300 == 0) // Every 5 seconds at 60 FPS
            {
                RefreshPawnList();
            }
        }

        /// <summary>
        /// Initializes references to other components
        /// </summary>
        private void InitializeReferences()
        {
            pawnGenerator = FindObjectOfType<PawnGenerator>();
        }

        /// <summary>
        /// Sets up button click listeners
        /// </summary>
        private void SetupButtonListeners()
        {
            if (previousButton != null)
                previousButton.onClick.AddListener(NavigatePrevious);

            if (nextButton != null)
                nextButton.onClick.AddListener(NavigateNext);

            if (firstButton != null)
                firstButton.onClick.AddListener(NavigateFirst);

            if (lastButton != null)
                lastButton.onClick.AddListener(NavigateLast);

            if (randomPawnButton != null)
                randomPawnButton.onClick.AddListener(NavigateToRandomPawn);

            if (mostHappyButton != null)
                mostHappyButton.onClick.AddListener(NavigateToMostHappy);

            if (leastHappyButton != null)
                leastHappyButton.onClick.AddListener(NavigateToLeastHappy);

            if (richestButton != null)
                richestButton.onClick.AddListener(NavigateToRichest);

            if (poorestButton != null)
                poorestButton.onClick.AddListener(NavigateToPoorest);

            if (clearFiltersButton != null)
                clearFiltersButton.onClick.AddListener(ClearFilters);

            // Search field listener
            if (searchField != null)
                searchField.onValueChanged.AddListener(OnSearchChanged);

            // Dropdown listeners
            if (sortDropdown != null)
                sortDropdown.onValueChanged.AddListener(OnSortChanged);

            if (filterDropdown != null)
                filterDropdown.onValueChanged.AddListener(OnFilterChanged);
        }

        /// <summary>
        /// Sets up dropdown options
        /// </summary>
        private void SetupDropdowns()
        {
            // Sort dropdown
            if (sortDropdown != null)
            {
                sortDropdown.ClearOptions();
                List<string> sortOptions = new List<string>
                {
                    "Name", "Age", "Satisfaction", "Income", "Ethnicity", "Job"
                };
                sortDropdown.AddOptions(sortOptions);
            }

            // Filter dropdown
            if (filterDropdown != null)
            {
                filterDropdown.ClearOptions();
                List<string> filterOptions = new List<string>
                {
                    "All", "Happy (>70%)", "Unhappy (<30%)", "Rich (>$500)", "Poor (<$100)",
                    "Young (<30)", "Old (>60)", "Male", "Female", "High Income", "Low Income"
                };
                filterDropdown.AddOptions(filterOptions);
            }
        }

        /// <summary>
        /// Refreshes the pawn list from the generator
        /// </summary>
        public void RefreshPawnList()
        {
            if (pawnGenerator == null) return;

            allPawns = pawnGenerator.GetAllPawns();
            ApplyFiltersAndSort();
            UpdateNavigationButtons();
            UpdateCounterDisplay();
        }

        /// <summary>
        /// Applies current filters and sorting to the pawn list
        /// </summary>
        private void ApplyFiltersAndSort()
        {
            // Start with all pawns
            filteredPawns = new List<Pawn>(allPawns);

            // Apply search filter
            if (!string.IsNullOrEmpty(currentSearchTerm))
            {
                filteredPawns = filteredPawns.FindAll(pawn =>
                    pawn.pawnName.ToLower().Contains(currentSearchTerm.ToLower()) ||
                    pawn.ethnicity.ToLower().Contains(currentSearchTerm.ToLower()) ||
                    pawn.currentWorkZone.ToString().ToLower().Contains(currentSearchTerm.ToLower())
                );
            }

            // Apply category filter
            switch (currentFilter)
            {
                case FilterCriteria.Happy:
                    filteredPawns = filteredPawns.FindAll(pawn => pawn.overallSatisfaction > 70f);
                    break;
                case FilterCriteria.Unhappy:
                    filteredPawns = filteredPawns.FindAll(pawn => pawn.overallSatisfaction < 30f);
                    break;
                case FilterCriteria.Rich:
                    filteredPawns = filteredPawns.FindAll(pawn => pawn.personalMoney > 500);
                    break;
                case FilterCriteria.Poor:
                    filteredPawns = filteredPawns.FindAll(pawn => pawn.personalMoney < 100);
                    break;
                case FilterCriteria.Young:
                    filteredPawns = filteredPawns.FindAll(pawn => pawn.age < 30);
                    break;
                case FilterCriteria.Old:
                    filteredPawns = filteredPawns.FindAll(pawn => pawn.age > 60);
                    break;
                case FilterCriteria.Male:
                    filteredPawns = filteredPawns.FindAll(pawn => pawn.gender == Gender.Male);
                    break;
                case FilterCriteria.Female:
                    filteredPawns = filteredPawns.FindAll(pawn => pawn.gender == Gender.Female);
                    break;
                case FilterCriteria.HighIncome:
                    filteredPawns = filteredPawns.FindAll(pawn => pawn.dailyIncome > 100f);
                    break;
                case FilterCriteria.LowIncome:
                    filteredPawns = filteredPawns.FindAll(pawn => pawn.dailyIncome < 50f);
                    break;
            }

            // Apply sorting
            switch (currentSort)
            {
                case SortCriteria.Name:
                    filteredPawns.Sort((a, b) => a.pawnName.CompareTo(b.pawnName));
                    break;
                case SortCriteria.Age:
                    filteredPawns.Sort((a, b) => a.age.CompareTo(b.age));
                    break;
                case SortCriteria.Satisfaction:
                    filteredPawns.Sort((a, b) => b.overallSatisfaction.CompareTo(a.overallSatisfaction));
                    break;
                case SortCriteria.Income:
                    filteredPawns.Sort((a, b) => b.dailyIncome.CompareTo(a.dailyIncome));
                    break;
                case SortCriteria.Ethnicity:
                    filteredPawns.Sort((a, b) => a.ethnicity.CompareTo(b.ethnicity));
                    break;
                case SortCriteria.Job:
                    filteredPawns.Sort((a, b) => a.currentWorkZone.CompareTo(b.currentWorkZone));
                    break;
            }

            // Adjust current index if needed
            if (currentIndex >= filteredPawns.Count)
            {
                currentIndex = Mathf.Max(0, filteredPawns.Count - 1);
            }
        }

        /// <summary>
        /// Navigates to the previous pawn
        /// </summary>
        public void NavigatePrevious()
        {
            if (filteredPawns.Count == 0) return;

            currentIndex = (currentIndex - 1 + filteredPawns.Count) % filteredPawns.Count;
            SelectCurrentPawn();
        }

        /// <summary>
        /// Navigates to the next pawn
        /// </summary>
        public void NavigateNext()
        {
            if (filteredPawns.Count == 0) return;

            currentIndex = (currentIndex + 1) % filteredPawns.Count;
            SelectCurrentPawn();
        }

        /// <summary>
        /// Navigates to the first pawn
        /// </summary>
        public void NavigateFirst()
        {
            if (filteredPawns.Count == 0) return;

            currentIndex = 0;
            SelectCurrentPawn();
        }

        /// <summary>
        /// Navigates to the last pawn
        /// </summary>
        public void NavigateLast()
        {
            if (filteredPawns.Count == 0) return;

            currentIndex = filteredPawns.Count - 1;
            SelectCurrentPawn();
        }

        /// <summary>
        /// Navigates to a random pawn
        /// </summary>
        public void NavigateToRandomPawn()
        {
            if (filteredPawns.Count == 0) return;

            currentIndex = Random.Range(0, filteredPawns.Count);
            SelectCurrentPawn();
        }

        /// <summary>
        /// Navigates to the most happy pawn
        /// </summary>
        public void NavigateToMostHappy()
        {
            if (filteredPawns.Count == 0) return;

            Pawn mostHappy = filteredPawns[0];
            int mostHappyIndex = 0;

            for (int i = 1; i < filteredPawns.Count; i++)
            {
                if (filteredPawns[i].overallSatisfaction > mostHappy.overallSatisfaction)
                {
                    mostHappy = filteredPawns[i];
                    mostHappyIndex = i;
                }
            }

            currentIndex = mostHappyIndex;
            SelectCurrentPawn();
        }

        /// <summary>
        /// Navigates to the least happy pawn
        /// </summary>
        public void NavigateToLeastHappy()
        {
            if (filteredPawns.Count == 0) return;

            Pawn leastHappy = filteredPawns[0];
            int leastHappyIndex = 0;

            for (int i = 1; i < filteredPawns.Count; i++)
            {
                if (filteredPawns[i].overallSatisfaction < leastHappy.overallSatisfaction)
                {
                    leastHappy = filteredPawns[i];
                    leastHappyIndex = i;
                }
            }

            currentIndex = leastHappyIndex;
            SelectCurrentPawn();
        }

        /// <summary>
        /// Navigates to the richest pawn
        /// </summary>
        public void NavigateToRichest()
        {
            if (filteredPawns.Count == 0) return;

            Pawn richest = filteredPawns[0];
            int richestIndex = 0;

            for (int i = 1; i < filteredPawns.Count; i++)
            {
                if (filteredPawns[i].personalMoney > richest.personalMoney)
                {
                    richest = filteredPawns[i];
                    richestIndex = i;
                }
            }

            currentIndex = richestIndex;
            SelectCurrentPawn();
        }

        /// <summary>
        /// Navigates to the poorest pawn
        /// </summary>
        public void NavigateToPoorest()
        {
            if (filteredPawns.Count == 0) return;

            Pawn poorest = filteredPawns[0];
            int poorestIndex = 0;

            for (int i = 1; i < filteredPawns.Count; i++)
            {
                if (filteredPawns[i].personalMoney < poorest.personalMoney)
                {
                    poorest = filteredPawns[i];
                    poorestIndex = i;
                }
            }

            currentIndex = poorestIndex;
            SelectCurrentPawn();
        }

        /// <summary>
        /// Clears all filters and search
        /// </summary>
        public void ClearFilters()
        {
            currentSearchTerm = "";
            currentFilter = FilterCriteria.All;
            currentSort = SortCriteria.Name;

            if (searchField != null) searchField.text = "";
            if (filterDropdown != null) filterDropdown.value = 0;
            if (sortDropdown != null) sortDropdown.value = 0;

            ApplyFiltersAndSort();
            UpdateNavigationButtons();
            UpdateCounterDisplay();
        }

        /// <summary>
        /// Selects the current pawn and notifies listeners
        /// </summary>
        private void SelectCurrentPawn()
        {
            if (filteredPawns.Count == 0) return;

            Pawn selectedPawn = filteredPawns[currentIndex];
            OnPawnSelected?.Invoke(selectedPawn);
            OnNavigationChanged?.Invoke(currentIndex, filteredPawns.Count);
            UpdateNavigationButtons();
            UpdateCounterDisplay();
        }

        /// <summary>
        /// Updates navigation button states
        /// </summary>
        private void UpdateNavigationButtons()
        {
            bool hasPawns = filteredPawns.Count > 0;

            if (previousButton != null) previousButton.interactable = hasPawns;
            if (nextButton != null) nextButton.interactable = hasPawns;
            if (firstButton != null) firstButton.interactable = hasPawns;
            if (lastButton != null) lastButton.interactable = hasPawns;
        }

        /// <summary>
        /// Updates the counter display
        /// </summary>
        private void UpdateCounterDisplay()
        {
            if (counterText != null)
            {
                if (filteredPawns.Count == 0)
                {
                    counterText.text = "No pawns";
                }
                else
                {
                    counterText.text = $"{currentIndex + 1} / {filteredPawns.Count}";
                    if (filteredPawns.Count != allPawns.Count)
                    {
                        counterText.text += $" (of {allPawns.Count} total)";
                    }
                }
            }
        }

        /// <summary>
        /// Called when search field changes
        /// </summary>
        private void OnSearchChanged(string searchTerm)
        {
            currentSearchTerm = searchTerm;
            ApplyFiltersAndSort();
            UpdateNavigationButtons();
            UpdateCounterDisplay();
        }

        /// <summary>
        /// Called when sort dropdown changes
        /// </summary>
        private void OnSortChanged(int sortIndex)
        {
            currentSort = (SortCriteria)sortIndex;
            ApplyFiltersAndSort();
            UpdateNavigationButtons();
            UpdateCounterDisplay();
        }

        /// <summary>
        /// Called when filter dropdown changes
        /// </summary>
        private void OnFilterChanged(int filterIndex)
        {
            currentFilter = (FilterCriteria)filterIndex;
            ApplyFiltersAndSort();
            UpdateNavigationButtons();
            UpdateCounterDisplay();
        }

        /// <summary>
        /// Gets the currently selected pawn
        /// </summary>
        public Pawn GetCurrentPawn()
        {
            if (filteredPawns.Count == 0 || currentIndex >= filteredPawns.Count)
                return null;

            return filteredPawns[currentIndex];
        }
    }

    public enum SortCriteria
    {
        Name = 0,
        Age = 1,
        Satisfaction = 2,
        Income = 3,
        Ethnicity = 4,
        Job = 5
    }

    public enum FilterCriteria
    {
        All = 0,
        Happy = 1,
        Unhappy = 2,
        Rich = 3,
        Poor = 4,
        Young = 5,
        Old = 6,
        Male = 7,
        Female = 8,
        HighIncome = 9,
        LowIncome = 10
    }
}
