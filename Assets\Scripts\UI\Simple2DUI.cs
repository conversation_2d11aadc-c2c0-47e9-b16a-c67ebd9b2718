using UnityEngine;
using RichBillionaire.Pawns;
using RichBillionaire.Manager;

namespace RichBillionaire.UI
{
    /// <summary>
    /// Simple 2D UI overlay that displays game information using Unity's OnGUI system
    /// Shows current pawn info, resources, and controls without requiring UI Canvas setup
    /// </summary>
    public class Simple2DUI : MonoBehaviour
    {
        [Header("UI Settings")]
        [SerializeField] private bool showUI = true;
        [SerializeField] private int fontSize = 16;
        [SerializeField] private Color textColor = Color.white;
        [SerializeField] private Color backgroundColor = new Color(0, 0, 0, 0.7f);

        // System references
        private PawnGenerator pawnGenerator;
        private PlayerResourceTracker resourceTracker;
        private TaxAndIncomeSystem taxSystem;

        // Current state
        private Pawn currentPawn;
        private int currentPawnIndex = 0;
        private float lastUpdate = 0f;
        private float updateInterval = 1f;

        // GUI Style
        private GUIStyle textStyle;
        private GUIStyle backgroundStyle;

        void Start()
        {
            // Find system references
            pawnGenerator = FindObjectOfType<PawnGenerator>();
            resourceTracker = FindObjectOfType<PlayerResourceTracker>();
            taxSystem = FindObjectOfType<TaxAndIncomeSystem>();

            // Initialize GUI styles
            InitializeGUIStyles();

            // Get first pawn
            UpdateCurrentPawn();
        }

        void Update()
        {
            // Handle input
            HandleInput();

            // Update display periodically
            if (Time.time - lastUpdate >= updateInterval)
            {
                UpdateCurrentPawn();
                lastUpdate = Time.time;
            }
        }

        void OnGUI()
        {
            if (!showUI) return;

            // Set up GUI styles
            if (textStyle == null) InitializeGUIStyles();

            // Draw background panels
            DrawBackgroundPanels();

            // Draw pawn information
            DrawPawnInfo();

            // Draw resource information
            DrawResourceInfo();

            // Draw controls
            DrawControls();

            // Draw population stats
            DrawPopulationStats();
        }

        /// <summary>
        /// Initializes GUI styles for consistent appearance
        /// </summary>
        private void InitializeGUIStyles()
        {
            textStyle = new GUIStyle(GUI.skin.label);
            textStyle.fontSize = fontSize;
            textStyle.normal.textColor = textColor;
            textStyle.wordWrap = true;

            backgroundStyle = new GUIStyle(GUI.skin.box);
            backgroundStyle.normal.background = CreateColorTexture(backgroundColor);
        }

        /// <summary>
        /// Creates a solid color texture for GUI backgrounds
        /// </summary>
        private Texture2D CreateColorTexture(Color color)
        {
            Texture2D texture = new Texture2D(1, 1);
            texture.SetPixel(0, 0, color);
            texture.Apply();
            return texture;
        }

        /// <summary>
        /// Draws background panels for UI sections
        /// </summary>
        private void DrawBackgroundPanels()
        {
            // Left panel for pawn info
            GUI.Box(new Rect(10, 10, 300, 200), "", backgroundStyle);

            // Top right panel for resources
            GUI.Box(new Rect(Screen.width - 310, 10, 300, 150), "", backgroundStyle);

            // Bottom left panel for controls
            GUI.Box(new Rect(10, 220, 300, 100), "", backgroundStyle);

            // Bottom right panel for population stats
            GUI.Box(new Rect(Screen.width - 310, 170, 300, 150), "", backgroundStyle);
        }

        /// <summary>
        /// Draws current pawn information
        /// </summary>
        private void DrawPawnInfo()
        {
            if (currentPawn == null) return;

            string pawnInfo = $"=== CURRENT PAWN ===\n";
            pawnInfo += $"Name: {currentPawn.pawnName}\n";
            pawnInfo += $"Age: {currentPawn.age} | Gender: {currentPawn.gender}\n";
            pawnInfo += $"Ethnicity: {currentPawn.ethnicity}\n";
            pawnInfo += $"Job: {currentPawn.currentWorkZone}\n";
            pawnInfo += $"Income: ${currentPawn.dailyIncome:F0}/day\n";
            pawnInfo += $"Money: ${currentPawn.personalMoney}\n";
            pawnInfo += $"Satisfaction: {currentPawn.overallSatisfaction:F0}%\n";
            pawnInfo += $"Basic Needs: {currentPawn.basicNeeds:F0}%\n";
            pawnInfo += $"Luxury Needs: {currentPawn.luxuryNeeds:F0}%\n";
            pawnInfo += $"Social Needs: {currentPawn.socialNeeds:F0}%";

            GUI.Label(new Rect(20, 20, 280, 180), pawnInfo, textStyle);
        }

        /// <summary>
        /// Draws resource information
        /// </summary>
        private void DrawResourceInfo()
        {
            if (resourceTracker == null) return;

            string resourceInfo = $"=== RESOURCES ===\n";
            resourceInfo += $"Money: ${resourceTracker.money:F0}\n";
            resourceInfo += $"Materials: {resourceTracker.materials:F0}\n";
            resourceInfo += $"Food: {resourceTracker.food:F0}\n";
            resourceInfo += $"Culture: {resourceTracker.culture:F0}\n";
            resourceInfo += $"Happiness: {resourceTracker.happiness:F0}\n";
            resourceInfo += $"Technology: {resourceTracker.technology:F0}\n";
            resourceInfo += $"Environment: {resourceTracker.environment:F0}\n";
            resourceInfo += $"Influence: {resourceTracker.influence:F0}";

            GUI.Label(new Rect(Screen.width - 300, 20, 280, 130), resourceInfo, textStyle);
        }

        /// <summary>
        /// Draws control instructions
        /// </summary>
        private void DrawControls()
        {
            string controls = $"=== CONTROLS ===\n";
            controls += $"← → : Navigate Pawns ({currentPawnIndex + 1}/{GetPawnCount()})\n";
            controls += $"SPACE: Pause/Resume\n";
            controls += $"T: Run Tests | P: New Pawn\n";
            controls += $"R: Add Resources | E: Random Event\n";
            controls += $"H: Toggle UI";

            GUI.Label(new Rect(20, 230, 280, 80), controls, textStyle);
        }

        /// <summary>
        /// Draws population statistics
        /// </summary>
        private void DrawPopulationStats()
        {
            if (pawnGenerator == null) return;

            string stats = $"=== POPULATION ===\n";
            stats += pawnGenerator.GetPopulationStats();

            if (taxSystem != null)
            {
                stats += $"\n\n=== ECONOMY ===\n";
                stats += $"Tax Rate: {taxSystem.incomeTaxRate:F0}%\n";
                stats += $"Daily Tax: ${taxSystem.dailyTaxRevenue:F0}\n";
                stats += $"Daily Subsidies: ${taxSystem.dailySubsidies:F0}";
            }

            GUI.Label(new Rect(Screen.width - 300, 180, 280, 130), stats, textStyle);
        }

        /// <summary>
        /// Handles keyboard input for navigation and controls
        /// </summary>
        private void HandleInput()
        {
            if (Input.GetKeyDown(KeyCode.LeftArrow))
            {
                NavigatePreviousPawn();
            }
            else if (Input.GetKeyDown(KeyCode.RightArrow))
            {
                NavigateNextPawn();
            }
            else if (Input.GetKeyDown(KeyCode.Space))
            {
                TogglePause();
            }
            else if (Input.GetKeyDown(KeyCode.H))
            {
                showUI = !showUI;
            }
            else if (Input.GetKeyDown(KeyCode.T))
            {
                RunTests();
            }
            else if (Input.GetKeyDown(KeyCode.P))
            {
                GenerateNewPawn();
            }
            else if (Input.GetKeyDown(KeyCode.R))
            {
                AddTestResources();
            }
            else if (Input.GetKeyDown(KeyCode.E))
            {
                TriggerRandomEvent();
            }
        }

        /// <summary>
        /// Updates the current pawn reference
        /// </summary>
        private void UpdateCurrentPawn()
        {
            if (pawnGenerator == null) return;

            var pawns = pawnGenerator.GetAllPawns();
            if (pawns.Count > 0)
            {
                currentPawnIndex = Mathf.Clamp(currentPawnIndex, 0, pawns.Count - 1);
                currentPawn = pawns[currentPawnIndex];
            }
        }

        /// <summary>
        /// Navigates to the previous pawn
        /// </summary>
        private void NavigatePreviousPawn()
        {
            if (pawnGenerator == null) return;

            var pawns = pawnGenerator.GetAllPawns();
            if (pawns.Count > 0)
            {
                currentPawnIndex = (currentPawnIndex - 1 + pawns.Count) % pawns.Count;
                currentPawn = pawns[currentPawnIndex];
                HighlightCurrentPawn();
            }
        }

        /// <summary>
        /// Navigates to the next pawn
        /// </summary>
        private void NavigateNextPawn()
        {
            if (pawnGenerator == null) return;

            var pawns = pawnGenerator.GetAllPawns();
            if (pawns.Count > 0)
            {
                currentPawnIndex = (currentPawnIndex + 1) % pawns.Count;
                currentPawn = pawns[currentPawnIndex];
                HighlightCurrentPawn();
            }
        }

        /// <summary>
        /// Highlights the current pawn by changing its scale
        /// </summary>
        private void HighlightCurrentPawn()
        {
            if (pawnGenerator == null) return;

            var pawns = pawnGenerator.GetAllPawns();
            
            // Reset all pawn scales
            foreach (var pawn in pawns)
            {
                if (pawn != null)
                    pawn.transform.localScale = Vector3.one;
            }

            // Highlight current pawn
            if (currentPawn != null)
            {
                currentPawn.transform.localScale = Vector3.one * 1.5f;
            }
        }

        /// <summary>
        /// Gets the total number of pawns
        /// </summary>
        private int GetPawnCount()
        {
            if (pawnGenerator == null) return 0;
            return pawnGenerator.GetAllPawns().Count;
        }

        /// <summary>
        /// Toggles game pause
        /// </summary>
        private void TogglePause()
        {
            Time.timeScale = Time.timeScale > 0 ? 0 : 1;
            Debug.Log(Time.timeScale > 0 ? "Game Resumed" : "Game Paused");
        }

        /// <summary>
        /// Runs system tests
        /// </summary>
        private void RunTests()
        {
            var testRunner = FindObjectOfType<RichBillionaire.System.TestRunner>();
            if (testRunner != null)
            {
                testRunner.RunAllTests();
            }
            else
            {
                Debug.Log("TestRunner not found");
            }
        }

        /// <summary>
        /// Generates a new pawn
        /// </summary>
        private void GenerateNewPawn()
        {
            if (pawnGenerator != null)
            {
                pawnGenerator.GenerateSinglePawn();
                Debug.Log("Generated new pawn");
            }
        }

        /// <summary>
        /// Adds test resources
        /// </summary>
        private void AddTestResources()
        {
            if (resourceTracker != null)
            {
                resourceTracker.AddResource(ResourceType.Money, 1000f);
                resourceTracker.AddResource(ResourceType.Materials, 100f);
                resourceTracker.AddResource(ResourceType.Food, 50f);
                Debug.Log("Added test resources");
            }
        }

        /// <summary>
        /// Triggers a random event
        /// </summary>
        private void TriggerRandomEvent()
        {
            var eventSystem = FindObjectOfType<RichBillionaire.System.EventSystem>();
            if (eventSystem != null)
            {
                eventSystem.TriggerDailyEvents();
                Debug.Log("Triggered random event");
            }
        }
    }
}
