using UnityEngine;
using RichBillionaire.Pawns;

namespace RichBillionaire.UI
{
    /// <summary>
    /// Displays detailed pawn statistics in a readable format
    /// Shows personality traits, work performance, and detailed needs breakdown
    /// </summary>
    public class StatsDisplayUI : MonoBehaviour
    {
        [Header("Display Settings")]
        [SerializeField] private bool enableDebugOutput = true;

        private Pawn currentPawn;

        void Start()
        {
            if (enableDebugOutput)
                Debug.Log("StatsDisplayUI initialized - Debug mode enabled");
        }

        /// <summary>
        /// Updates the display with a new pawn's stats (debug version)
        /// </summary>
        public void UpdateDisplay(Pawn pawn)
        {
            currentPawn = pawn;

            if (pawn == null)
            {
                if (enableDebugOutput)
                    Debug.Log("No pawn selected for stats display");
                return;
            }

            if (enableDebugOutput)
            {
                string statsInfo = $"=== {pawn.pawnName} Stats ===\n";
                statsInfo += $"Intellect: {pawn.stats.intellect} | Morality: {pawn.stats.morality}\n";
                statsInfo += $"Ambition: {pawn.stats.ambition} | Strength: {pawn.stats.strength}\n";
                statsInfo += $"Productivity: {pawn.productivityMultiplier:F2}x\n";
                statsInfo += $"Job: {pawn.currentWorkZone} | Income: ${pawn.dailyIncome:F0}/day\n";
                statsInfo += $"Satisfaction: {pawn.overallSatisfaction:F0}%";
                Debug.Log(statsInfo);
            }
        }

        /// <summary>
        /// Gets the best job recommendations for the current pawn
        /// </summary>
        public string GetJobRecommendations()
        {
            if (currentPawn == null) return "No pawn selected";

            var stats = currentPawn.stats;
            string result = $"Best Jobs for {currentPawn.pawnName}:\n";

            // Calculate effectiveness for each zone type
            float bestEffectiveness = 0f;
            ZoneType bestZone = ZoneType.Industrial;

            foreach (ZoneType zoneType in global::System.Enum.GetValues(typeof(ZoneType)))
            {
                float effectiveness = stats.GetZoneEffectiveness(zoneType);
                if (effectiveness > bestEffectiveness)
                {
                    bestEffectiveness = effectiveness;
                    bestZone = zoneType;
                }
            }

            result += $"1. {bestZone} ({bestEffectiveness:F2}x effectiveness)";
            return result;
        }

        /// <summary>
        /// Gets work suitability description
        /// </summary>
        public string GetWorkSuitability(Pawn pawn)
        {
            if (pawn == null) return "No pawn";

            float effectiveness = pawn.stats.GetZoneEffectiveness(pawn.currentWorkZone);

            if (effectiveness > 1.3f) return "Excellent";
            if (effectiveness > 1.1f) return "Good";
            if (effectiveness > 0.9f) return "Average";
            if (effectiveness > 0.7f) return "Poor";
            return "Very Poor";
        }

        /// <summary>
        /// Gets wealth level description
        /// </summary>
        public string GetWealthLevel(int money)
        {
            if (money > 1000) return "Wealthy";
            if (money > 500) return "Comfortable";
            if (money > 200) return "Modest";
            if (money > 50) return "Struggling";
            return "Poor";
        }

    }
}
