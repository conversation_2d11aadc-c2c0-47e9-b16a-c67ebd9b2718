using UnityEngine;
using UnityEngine.UI;
using TMPro;
using RichBillionaire.Pawns;

namespace RichBillionaire.UI
{
    /// <summary>
    /// Displays detailed pawn statistics in a readable format
    /// Shows personality traits, work performance, and detailed needs breakdown
    /// </summary>
    public class StatsDisplayUI : MonoBehaviour
    {
        [Header("Personality Stats")]
        [SerializeField] private Slider intellectSlider;
        [SerializeField] private Slider moralitySlider;
        [SerializeField] private Slider ambitionSlider;
        [SerializeField] private Slider strengthSlider;
        [SerializeField] private Slider sociabilitySlider;
        [SerializeField] private Slider creativitySlider;
        [SerializeField] private Slider stabilitySlider;
        [SerializeField] private Slider materialismSlider;

        [Header("Personality Labels")]
        [SerializeField] private TextMeshProUGUI intellectLabel;
        [SerializeField] private TextMeshProUGUI moralityLabel;
        [SerializeField] private TextMeshProUGUI ambitionLabel;
        [SerializeField] private TextMeshProUGUI strengthLabel;
        [SerializeField] private TextMeshProUGUI sociabilityLabel;
        [SerializeField] private TextMeshProUGUI creativityLabel;
        [SerializeField] private TextMeshProUGUI stabilityLabel;
        [SerializeField] private TextMeshProUGUI materialismLabel;

        [Header("Secondary Traits")]
        [SerializeField] private Slider loyaltySlider;
        [SerializeField] private Slider patienceSlider;
        [SerializeField] private Slider optimismSlider;
        [SerializeField] private Slider conformitySlider;

        [Header("Secondary Labels")]
        [SerializeField] private TextMeshProUGUI loyaltyLabel;
        [SerializeField] private TextMeshProUGUI patienceLabel;
        [SerializeField] private TextMeshProUGUI optimismLabel;
        [SerializeField] private TextMeshProUGUI conformityLabel;

        [Header("Work Performance")]
        [SerializeField] private TextMeshProUGUI productivityText;
        [SerializeField] private TextMeshProUGUI zoneEffectivenessText;
        [SerializeField] private TextMeshProUGUI workSuitabilityText;

        [Header("Detailed Info")]
        [SerializeField] private TextMeshProUGUI personalityDescriptionText;
        [SerializeField] private TextMeshProUGUI relationshipsText;
        [SerializeField] private TextMeshProUGUI financialStatusText;
        [SerializeField] private TextMeshProUGUI primaryDesireText;

        [Header("Color Coding")]
        [SerializeField] private Color positiveColor = Color.green;
        [SerializeField] private Color neutralColor = Color.yellow;
        [SerializeField] private Color negativeColor = Color.red;

        private Pawn currentPawn;

        void Start()
        {
            InitializeSliders();
        }

        /// <summary>
        /// Initializes all sliders with proper ranges
        /// </summary>
        private void InitializeSliders()
        {
            // Set all sliders to range from -100 to +100
            Slider[] allSliders = {
                intellectSlider, moralitySlider, ambitionSlider, strengthSlider,
                sociabilitySlider, creativitySlider, stabilitySlider, materialismSlider,
                loyaltySlider, patienceSlider, optimismSlider, conformitySlider
            };

            foreach (var slider in allSliders)
            {
                if (slider != null)
                {
                    slider.minValue = -100f;
                    slider.maxValue = 100f;
                    slider.value = 0f;
                }
            }
        }

        /// <summary>
        /// Updates the display with a new pawn's stats
        /// </summary>
        public void UpdateDisplay(Pawn pawn)
        {
            currentPawn = pawn;
            
            if (pawn == null)
            {
                ClearDisplay();
                return;
            }

            UpdatePersonalityStats(pawn.stats);
            UpdateWorkPerformance(pawn);
            UpdateDetailedInfo(pawn);
        }

        /// <summary>
        /// Updates personality stat sliders and labels
        /// </summary>
        private void UpdatePersonalityStats(PawnStats stats)
        {
            // Core attributes
            UpdateStatSlider(intellectSlider, intellectLabel, stats.intellect, "Smart", "Dumb");
            UpdateStatSlider(moralitySlider, moralityLabel, stats.morality, "Good", "Evil");
            UpdateStatSlider(ambitionSlider, ambitionLabel, stats.ambition, "Driven", "Lazy");
            UpdateStatSlider(strengthSlider, strengthLabel, stats.strength, "Strong", "Weak");
            UpdateStatSlider(sociabilitySlider, sociabilityLabel, stats.sociability, "Social", "Antisocial");
            UpdateStatSlider(creativitySlider, creativityLabel, stats.creativity, "Creative", "Conventional");
            UpdateStatSlider(stabilitySlider, stabilityLabel, stats.stability, "Stable", "Chaotic");
            UpdateStatSlider(materialismSlider, materialismLabel, stats.materialism, "Materialistic", "Spiritual");

            // Secondary traits
            UpdateStatSlider(loyaltySlider, loyaltyLabel, stats.loyalty, "Loyal", "Rebellious");
            UpdateStatSlider(patienceSlider, patienceLabel, stats.patience, "Patient", "Impulsive");
            UpdateStatSlider(optimismSlider, optimismLabel, stats.optimism, "Optimistic", "Pessimistic");
            UpdateStatSlider(conformitySlider, conformityLabel, stats.conformity, "Conformist", "Independent");
        }

        /// <summary>
        /// Updates a single stat slider and its label
        /// </summary>
        private void UpdateStatSlider(Slider slider, TextMeshProUGUI label, int statValue, string positiveLabel, string negativeLabel)
        {
            if (slider == null) return;

            slider.value = statValue;

            if (label != null)
            {
                string traitLabel = "";
                Color labelColor = neutralColor;

                if (statValue > 30)
                {
                    traitLabel = positiveLabel;
                    labelColor = positiveColor;
                }
                else if (statValue < -30)
                {
                    traitLabel = negativeLabel;
                    labelColor = negativeColor;
                }
                else
                {
                    traitLabel = "Neutral";
                    labelColor = neutralColor;
                }

                label.text = $"{traitLabel} ({statValue:+0;-0;0})";
                label.color = labelColor;
            }

            // Update slider colors
            var sliderColors = slider.colors;
            if (statValue > 30)
                sliderColors.normalColor = positiveColor;
            else if (statValue < -30)
                sliderColors.normalColor = negativeColor;
            else
                sliderColors.normalColor = neutralColor;
            slider.colors = sliderColors;
        }

        /// <summary>
        /// Updates work performance information
        /// </summary>
        private void UpdateWorkPerformance(Pawn pawn)
        {
            if (productivityText != null)
            {
                productivityText.text = $"Productivity: {pawn.productivityMultiplier:F2}x";
                productivityText.color = GetPerformanceColor(pawn.productivityMultiplier, 1f);
            }

            if (zoneEffectivenessText != null)
            {
                float effectiveness = pawn.stats.GetZoneEffectiveness(pawn.currentWorkZone);
                zoneEffectivenessText.text = $"Zone Effectiveness: {effectiveness:F2}x";
                zoneEffectivenessText.color = GetPerformanceColor(effectiveness, 1f);
            }

            if (workSuitabilityText != null)
            {
                string suitability = GetWorkSuitability(pawn);
                workSuitabilityText.text = $"Job Suitability: {suitability}";
            }
        }

        /// <summary>
        /// Gets work suitability description
        /// </summary>
        private string GetWorkSuitability(Pawn pawn)
        {
            float effectiveness = pawn.stats.GetZoneEffectiveness(pawn.currentWorkZone);
            
            if (effectiveness > 1.3f) return "Excellent";
            if (effectiveness > 1.1f) return "Good";
            if (effectiveness > 0.9f) return "Average";
            if (effectiveness > 0.7f) return "Poor";
            return "Very Poor";
        }

        /// <summary>
        /// Updates detailed information about the pawn
        /// </summary>
        private void UpdateDetailedInfo(Pawn pawn)
        {
            if (personalityDescriptionText != null)
            {
                personalityDescriptionText.text = pawn.stats.GetPersonalityDescription();
            }

            if (relationshipsText != null)
            {
                string relationships = $"Friends: {pawn.friendsCount} | Enemies: {pawn.enemiesCount}";
                if (pawn.hasPartner) relationships += " | Has Partner";
                if (pawn.childrenCount > 0) relationships += $" | Children: {pawn.childrenCount}";
                relationshipsText.text = relationships;
            }

            if (financialStatusText != null)
            {
                string financial = $"Personal Money: ${pawn.personalMoney}";
                financial += $"\nDaily Income: ${pawn.dailyIncome:F0}";
                
                string wealthLevel = GetWealthLevel(pawn.personalMoney);
                financial += $"\nWealth Level: {wealthLevel}";
                
                financialStatusText.text = financial;
            }

            if (primaryDesireText != null)
            {
                primaryDesireText.text = $"Primary Desire: {pawn.primaryDesire}";
                primaryDesireText.color = GetDesireColor(pawn.primaryDesire);
            }
        }

        /// <summary>
        /// Gets wealth level description
        /// </summary>
        private string GetWealthLevel(int money)
        {
            if (money > 1000) return "Wealthy";
            if (money > 500) return "Comfortable";
            if (money > 200) return "Modest";
            if (money > 50) return "Struggling";
            return "Poor";
        }

        /// <summary>
        /// Gets color based on performance value
        /// </summary>
        private Color GetPerformanceColor(float value, float baseline)
        {
            if (value > baseline * 1.2f) return positiveColor;
            if (value < baseline * 0.8f) return negativeColor;
            return neutralColor;
        }

        /// <summary>
        /// Gets color based on primary desire
        /// </summary>
        private Color GetDesireColor(PrimaryDesire desire)
        {
            switch (desire)
            {
                case PrimaryDesire.Wealth: return Color.yellow;
                case PrimaryDesire.Power: return Color.red;
                case PrimaryDesire.Knowledge: return Color.blue;
                case PrimaryDesire.Love: return Color.magenta;
                case PrimaryDesire.Fame: return Color.cyan;
                case PrimaryDesire.Freedom: return Color.green;
                case PrimaryDesire.Security: return Color.gray;
                case PrimaryDesire.Adventure: return Color.orange;
                case PrimaryDesire.Creativity: return Color.violet;
                case PrimaryDesire.Service: return Color.white;
                default: return neutralColor;
            }
        }

        /// <summary>
        /// Clears the display when no pawn is selected
        /// </summary>
        private void ClearDisplay()
        {
            // Reset all sliders to 0
            Slider[] allSliders = {
                intellectSlider, moralitySlider, ambitionSlider, strengthSlider,
                sociabilitySlider, creativitySlider, stabilitySlider, materialismSlider,
                loyaltySlider, patienceSlider, optimismSlider, conformitySlider
            };

            foreach (var slider in allSliders)
            {
                if (slider != null) slider.value = 0f;
            }

            // Clear all text fields
            TextMeshProUGUI[] allTexts = {
                intellectLabel, moralityLabel, ambitionLabel, strengthLabel,
                sociabilityLabel, creativityLabel, stabilityLabel, materialismLabel,
                loyaltyLabel, patienceLabel, optimismLabel, conformityLabel,
                productivityText, zoneEffectivenessText, workSuitabilityText,
                personalityDescriptionText, relationshipsText, financialStatusText, primaryDesireText
            };

            foreach (var text in allTexts)
            {
                if (text != null) text.text = "";
            }
        }

        /// <summary>
        /// Gets the best job recommendations for the current pawn
        /// </summary>
        public string GetJobRecommendations()
        {
            if (currentPawn == null) return "No pawn selected";

            var stats = currentPawn.stats;
            var recommendations = new System.Collections.Generic.List<(ZoneType zone, float effectiveness)>();

            // Calculate effectiveness for each zone type
            foreach (ZoneType zoneType in System.Enum.GetValues(typeof(ZoneType)))
            {
                float effectiveness = stats.GetZoneEffectiveness(zoneType);
                recommendations.Add((zoneType, effectiveness));
            }

            // Sort by effectiveness
            recommendations.Sort((a, b) => b.effectiveness.CompareTo(a.effectiveness));

            string result = "Best Jobs:\n";
            for (int i = 0; i < Mathf.Min(3, recommendations.Count); i++)
            {
                result += $"{i + 1}. {recommendations[i].zone} ({recommendations[i].effectiveness:F2}x)\n";
            }

            return result;
        }
    }
}
