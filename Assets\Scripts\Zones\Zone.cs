using System.Collections.Generic;
using UnityEngine;
using RichBillionaire.Pawns;

namespace RichBillionaire.Zones
{
    /// <summary>
    /// Base zone class representing different areas where pawns can work and live
    /// Zones generate resources based on the pawns working in them
    /// </summary>
    public class Zone : MonoBehaviour
    {
        [Header("Zone Identity")]
        public string zoneName;
        public ZoneType zoneType;
        public string description;
        
        [Header("Zone Properties")]
        [Range(1, 10)] public int zoneLevel = 1;
        [Range(0, 100)] public float efficiency = 100f;
        [Range(0, 1000)] public int maxWorkers = 50;
        
        [Header("Resource Generation")]
        public float baseResourceGeneration = 10f;
        public float currentResourceGeneration = 0f;
        public float totalResourcesGenerated = 0f;
        
        [Header("Zone Costs")]
        public int buildCost = 1000;
        public int upgradeCost = 500;
        public int maintenanceCost = 50;
        
        // Workers in this zone
        private List<Pawn> workers = new List<Pawn>();
        
        // Resource generation component
        private ZoneResourceGenerator resourceGenerator;
        
        // Events
        public System.Action<Zone, float> OnResourcesGenerated;
        public System.Action<Zone, Pawn> OnWorkerAdded;
        public System.Action<Zone, Pawn> OnWorkerRemoved;
        public System.Action<Zone, int> OnZoneUpgraded;

        void Start()
        {
            // Get or add resource generator
            resourceGenerator = GetComponent<ZoneResourceGenerator>();
            if (resourceGenerator == null)
            {
                resourceGenerator = gameObject.AddComponent<ZoneResourceGenerator>();
            }
            
            resourceGenerator.Initialize(this);
            
            // Set default name if not set
            if (string.IsNullOrEmpty(zoneName))
            {
                zoneName = $"{zoneType} Zone";
            }
        }

        void Update()
        {
            // Update current resource generation based on workers
            CalculateResourceGeneration();
        }

        /// <summary>
        /// Calculates current resource generation based on workers and their stats
        /// </summary>
        private void CalculateResourceGeneration()
        {
            if (workers.Count == 0)
            {
                currentResourceGeneration = 0f;
                return;
            }

            float totalEffectiveness = 0f;
            
            foreach (Pawn worker in workers)
            {
                if (worker != null)
                {
                    float workerEffectiveness = worker.stats.GetZoneEffectiveness(zoneType);
                    totalEffectiveness += workerEffectiveness;
                }
            }

            // Calculate base generation
            currentResourceGeneration = baseResourceGeneration * (totalEffectiveness / workers.Count);
            
            // Apply zone level multiplier
            currentResourceGeneration *= (1f + (zoneLevel - 1) * 0.2f);
            
            // Apply efficiency
            currentResourceGeneration *= (efficiency / 100f);
        }

        /// <summary>
        /// Adds a worker to this zone
        /// </summary>
        public bool AddWorker(Pawn pawn)
        {
            if (pawn == null || workers.Contains(pawn) || workers.Count >= maxWorkers)
            {
                return false;
            }

            workers.Add(pawn);
            pawn.AssignToZone(zoneType);
            
            OnWorkerAdded?.Invoke(this, pawn);
            Debug.Log($"{pawn.pawnName} started working in {zoneName}");
            
            return true;
        }

        /// <summary>
        /// Removes a worker from this zone
        /// </summary>
        public bool RemoveWorker(Pawn pawn)
        {
            if (pawn == null || !workers.Contains(pawn))
            {
                return false;
            }

            workers.Remove(pawn);
            OnWorkerRemoved?.Invoke(this, pawn);
            Debug.Log($"{pawn.pawnName} stopped working in {zoneName}");
            
            return true;
        }

        /// <summary>
        /// Upgrades the zone to the next level
        /// </summary>
        public bool UpgradeZone()
        {
            if (zoneLevel >= 10)
            {
                Debug.Log($"{zoneName} is already at maximum level");
                return false;
            }

            zoneLevel++;
            maxWorkers += 10;
            baseResourceGeneration *= 1.2f;
            upgradeCost = Mathf.RoundToInt(upgradeCost * 1.5f);
            
            OnZoneUpgraded?.Invoke(this, zoneLevel);
            Debug.Log($"{zoneName} upgraded to level {zoneLevel}");
            
            return true;
        }

        /// <summary>
        /// Gets zone statistics
        /// </summary>
        public ZoneStats GetZoneStats()
        {
            return new ZoneStats
            {
                zoneName = this.zoneName,
                zoneType = this.zoneType,
                level = this.zoneLevel,
                workerCount = this.workers.Count,
                maxWorkers = this.maxWorkers,
                efficiency = this.efficiency,
                currentGeneration = this.currentResourceGeneration,
                totalGenerated = this.totalResourcesGenerated,
                buildCost = this.buildCost,
                upgradeCost = this.upgradeCost,
                maintenanceCost = this.maintenanceCost
            };
        }

        /// <summary>
        /// Gets all workers in this zone
        /// </summary>
        public List<Pawn> GetWorkers()
        {
            return new List<Pawn>(workers);
        }

        /// <summary>
        /// Gets the most productive worker in this zone
        /// </summary>
        public Pawn GetMostProductiveWorker()
        {
            if (workers.Count == 0) return null;

            Pawn mostProductive = workers[0];
            float highestProductivity = mostProductive.productivityMultiplier;

            foreach (Pawn worker in workers)
            {
                if (worker.productivityMultiplier > highestProductivity)
                {
                    highestProductivity = worker.productivityMultiplier;
                    mostProductive = worker;
                }
            }

            return mostProductive;
        }

        /// <summary>
        /// Gets the average satisfaction of workers in this zone
        /// </summary>
        public float GetAverageWorkerSatisfaction()
        {
            if (workers.Count == 0) return 0f;

            float totalSatisfaction = 0f;
            foreach (Pawn worker in workers)
            {
                totalSatisfaction += worker.overallSatisfaction;
            }

            return totalSatisfaction / workers.Count;
        }

        /// <summary>
        /// Applies a temporary efficiency modifier (for events, relics, etc.)
        /// </summary>
        public void ApplyEfficiencyModifier(float modifier, float duration)
        {
            StartCoroutine(TemporaryEfficiencyModifier(modifier, duration));
        }

        private System.Collections.IEnumerator TemporaryEfficiencyModifier(float modifier, float duration)
        {
            float originalEfficiency = efficiency;
            efficiency *= modifier;
            efficiency = Mathf.Clamp(efficiency, 0f, 200f);
            
            Debug.Log($"{zoneName} efficiency modified by {modifier:F2}x for {duration} seconds");
            
            yield return new WaitForSeconds(duration);
            
            efficiency = originalEfficiency;
            Debug.Log($"{zoneName} efficiency returned to normal");
        }

        /// <summary>
        /// Gets a description of the zone's current state
        /// </summary>
        public string GetZoneDescription()
        {
            string desc = $"{zoneName} (Level {zoneLevel})\n";
            desc += $"Type: {zoneType}\n";
            desc += $"Workers: {workers.Count}/{maxWorkers}\n";
            desc += $"Efficiency: {efficiency:F0}%\n";
            desc += $"Generation: {currentResourceGeneration:F1}/day\n";
            desc += $"Avg Satisfaction: {GetAverageWorkerSatisfaction():F0}%\n";
            desc += $"Total Generated: {totalResourcesGenerated:F0}";
            
            return desc;
        }

        /// <summary>
        /// Called when resources are generated (from ZoneResourceGenerator)
        /// </summary>
        public void OnResourceGenerated(float amount)
        {
            totalResourcesGenerated += amount;
            OnResourcesGenerated?.Invoke(this, amount);
        }
    }

    /// <summary>
    /// Data structure for zone statistics
    /// </summary>
    [System.Serializable]
    public struct ZoneStats
    {
        public string zoneName;
        public ZoneType zoneType;
        public int level;
        public int workerCount;
        public int maxWorkers;
        public float efficiency;
        public float currentGeneration;
        public float totalGenerated;
        public int buildCost;
        public int upgradeCost;
        public int maintenanceCost;
    }
}
