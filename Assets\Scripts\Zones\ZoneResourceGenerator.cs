using UnityEngine;
using RichBillionaire.Pawns;

namespace RichBillionaire.Zones
{
    /// <summary>
    /// Handles resource generation for zones based on worker stats and zone properties
    /// Generates different types of resources depending on zone type
    /// </summary>
    public class ZoneResourceGenerator : MonoBehaviour
    {
        [Header("Generation Settings")]
        [SerializeField] private float generationInterval = 5f; // Generate resources every 5 seconds
        [SerializeField] private bool autoGenerate = true;
        
        [Header("Resource Types Generated")]
        public ResourceType primaryResource;
        public ResourceType secondaryResource;
        
        [Header("Generation Rates")]
        [Range(0.1f, 10f)] public float primaryResourceRate = 1f;
        [Range(0.1f, 5f)] public float secondaryResourceRate = 0.5f;
        
        // References
        private Zone parentZone;
        private float lastGenerationTime;
        
        // Resource generation tracking
        private float totalPrimaryGenerated = 0f;
        private float totalSecondaryGenerated = 0f;

        /// <summary>
        /// Initializes the resource generator with a parent zone
        /// </summary>
        public void Initialize(Zone zone)
        {
            parentZone = zone;
            SetResourceTypesForZone(zone.zoneType);
            lastGenerationTime = Time.time;
        }

        void Update()
        {
            if (autoGenerate && parentZone != null && Time.time - lastGenerationTime >= generationInterval)
            {
                GenerateResources();
                lastGenerationTime = Time.time;
            }
        }

        /// <summary>
        /// Sets appropriate resource types based on zone type
        /// </summary>
        private void SetResourceTypesForZone(ZoneType zoneType)
        {
            switch (zoneType)
            {
                case ZoneType.Industrial:
                    primaryResource = ResourceType.Materials;
                    secondaryResource = ResourceType.Money;
                    break;
                case ZoneType.Farming:
                    primaryResource = ResourceType.Food;
                    secondaryResource = ResourceType.Materials;
                    break;
                case ZoneType.Cultural:
                    primaryResource = ResourceType.Culture;
                    secondaryResource = ResourceType.Influence;
                    break;
                case ZoneType.Mystic:
                    primaryResource = ResourceType.Faith;
                    secondaryResource = ResourceType.Culture;
                    break;
                case ZoneType.Vice:
                    primaryResource = ResourceType.Money;
                    secondaryResource = ResourceType.Corruption;
                    break;
                case ZoneType.TechHub:
                    primaryResource = ResourceType.Technology;
                    secondaryResource = ResourceType.Money;
                    break;
                case ZoneType.Entertainment:
                    primaryResource = ResourceType.Happiness;
                    secondaryResource = ResourceType.Money;
                    break;
                case ZoneType.Environmental:
                    primaryResource = ResourceType.Environment;
                    secondaryResource = ResourceType.Health;
                    break;
                default:
                    primaryResource = ResourceType.Money;
                    secondaryResource = ResourceType.Materials;
                    break;
            }
        }

        /// <summary>
        /// Generates resources based on current zone state and workers
        /// </summary>
        public void GenerateResources()
        {
            if (parentZone == null) return;

            float baseGeneration = parentZone.currentResourceGeneration;
            if (baseGeneration <= 0) return;

            // Calculate primary resource generation
            float primaryAmount = baseGeneration * primaryResourceRate * (generationInterval / 60f); // Per minute rate
            
            // Calculate secondary resource generation
            float secondaryAmount = baseGeneration * secondaryResourceRate * (generationInterval / 60f);

            // Apply worker-specific bonuses
            ApplyWorkerBonuses(ref primaryAmount, ref secondaryAmount);

            // Generate the resources
            if (primaryAmount > 0)
            {
                GenerateResource(primaryResource, primaryAmount);
                totalPrimaryGenerated += primaryAmount;
            }

            if (secondaryAmount > 0)
            {
                GenerateResource(secondaryResource, secondaryAmount);
                totalSecondaryGenerated += secondaryAmount;
            }

            // Notify parent zone
            parentZone.OnResourceGenerated(primaryAmount + secondaryAmount);

            Debug.Log($"{parentZone.zoneName} generated {primaryAmount:F1} {primaryResource} and {secondaryAmount:F1} {secondaryResource}");
        }

        /// <summary>
        /// Applies worker-specific bonuses to resource generation
        /// </summary>
        private void ApplyWorkerBonuses(ref float primaryAmount, ref float secondaryAmount)
        {
            var workers = parentZone.GetWorkers();
            if (workers.Count == 0) return;

            float totalBonus = 0f;
            float happinessBonus = 0f;

            foreach (Pawn worker in workers)
            {
                if (worker == null) continue;

                // Productivity bonus
                totalBonus += worker.productivityMultiplier;

                // Happiness affects quality of work
                happinessBonus += worker.overallSatisfaction * 0.01f;

                // Special stat bonuses based on zone type
                ApplySpecialStatBonuses(worker, ref primaryAmount, ref secondaryAmount);
            }

            // Apply average bonuses
            float avgBonus = totalBonus / workers.Count;
            float avgHappiness = happinessBonus / workers.Count;

            primaryAmount *= avgBonus * (0.8f + avgHappiness * 0.2f);
            secondaryAmount *= avgBonus * (0.8f + avgHappiness * 0.2f);
        }

        /// <summary>
        /// Applies special stat bonuses based on worker characteristics and zone type
        /// </summary>
        private void ApplySpecialStatBonuses(Pawn worker, ref float primaryAmount, ref float secondaryAmount)
        {
            switch (parentZone.zoneType)
            {
                case ZoneType.Industrial:
                    if (worker.stats.strength > 50) primaryAmount *= 1.1f;
                    if (worker.stats.ambition > 50) secondaryAmount *= 1.1f;
                    break;
                    
                case ZoneType.Farming:
                    if (worker.stats.patience > 50) primaryAmount *= 1.15f;
                    if (worker.stats.stability > 50) primaryAmount *= 1.1f;
                    break;
                    
                case ZoneType.Cultural:
                    if (worker.stats.creativity > 50) primaryAmount *= 1.2f;
                    if (worker.stats.intellect > 50) secondaryAmount *= 1.15f;
                    break;
                    
                case ZoneType.Mystic:
                    if (worker.stats.materialism < -30) primaryAmount *= 1.2f;
                    if (worker.stats.morality > 50) primaryAmount *= 1.1f;
                    break;
                    
                case ZoneType.Vice:
                    if (worker.stats.morality < -30) primaryAmount *= 1.15f;
                    if (worker.stats.materialism > 50) secondaryAmount *= 1.1f;
                    break;
                    
                case ZoneType.TechHub:
                    if (worker.stats.intellect > 50) primaryAmount *= 1.2f;
                    if (worker.stats.creativity > 50) primaryAmount *= 1.1f;
                    break;
                    
                case ZoneType.Entertainment:
                    if (worker.stats.sociability > 50) primaryAmount *= 1.15f;
                    if (worker.stats.creativity > 50) primaryAmount *= 1.1f;
                    break;
                    
                case ZoneType.Environmental:
                    if (worker.stats.morality > 50) primaryAmount *= 1.1f;
                    if (worker.stats.materialism < -30) primaryAmount *= 1.15f;
                    break;
            }
        }

        /// <summary>
        /// Generates a specific resource and notifies the game manager
        /// </summary>
        private void GenerateResource(ResourceType resourceType, float amount)
        {
            // Find the resource manager and add resources
            var resourceManager = FindObjectOfType<RichBillionaire.Manager.PlayerResourceTracker>();
            if (resourceManager != null)
            {
                resourceManager.AddResource(resourceType, amount);
            }
        }

        /// <summary>
        /// Gets generation statistics for this zone
        /// </summary>
        public GenerationStats GetGenerationStats()
        {
            return new GenerationStats
            {
                primaryResource = this.primaryResource,
                secondaryResource = this.secondaryResource,
                primaryResourceRate = this.primaryResourceRate,
                secondaryResourceRate = this.secondaryResourceRate,
                totalPrimaryGenerated = this.totalPrimaryGenerated,
                totalSecondaryGenerated = this.totalSecondaryGenerated,
                generationInterval = this.generationInterval
            };
        }

        /// <summary>
        /// Manually triggers resource generation (for testing or special events)
        /// </summary>
        public void ForceGeneration()
        {
            GenerateResources();
        }

        /// <summary>
        /// Sets custom generation rates (for relics, events, etc.)
        /// </summary>
        public void SetGenerationRates(float primaryRate, float secondaryRate)
        {
            primaryResourceRate = primaryRate;
            secondaryResourceRate = secondaryRate;
        }

        /// <summary>
        /// Applies a temporary generation boost
        /// </summary>
        public void ApplyGenerationBoost(float multiplier, float duration)
        {
            StartCoroutine(TemporaryGenerationBoost(multiplier, duration));
        }

        private System.Collections.IEnumerator TemporaryGenerationBoost(float multiplier, float duration)
        {
            float originalPrimary = primaryResourceRate;
            float originalSecondary = secondaryResourceRate;
            
            primaryResourceRate *= multiplier;
            secondaryResourceRate *= multiplier;
            
            Debug.Log($"{parentZone.zoneName} generation boosted by {multiplier:F2}x for {duration} seconds");
            
            yield return new WaitForSeconds(duration);
            
            primaryResourceRate = originalPrimary;
            secondaryResourceRate = originalSecondary;
            
            Debug.Log($"{parentZone.zoneName} generation boost ended");
        }
    }

    /// <summary>
    /// Types of resources that can be generated
    /// </summary>
    public enum ResourceType
    {
        Money,          // Currency for purchases
        Materials,      // Building and crafting materials
        Food,           // Sustenance for population
        Culture,        // Arts, education, refinement
        Faith,          // Religious/spiritual influence
        Technology,     // Research and innovation
        Happiness,      // Population satisfaction
        Environment,    // Ecological health
        Health,         // Medical and wellness
        Influence,      // Political and social power
        Corruption      // Negative influence and crime
    }

    /// <summary>
    /// Statistics for resource generation
    /// </summary>
    [System.Serializable]
    public struct GenerationStats
    {
        public ResourceType primaryResource;
        public ResourceType secondaryResource;
        public float primaryResourceRate;
        public float secondaryResourceRate;
        public float totalPrimaryGenerated;
        public float totalSecondaryGenerated;
        public float generationInterval;
    }
}
