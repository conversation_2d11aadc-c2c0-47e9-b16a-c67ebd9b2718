{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 3528, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 3528, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 3528, "tid": 258, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 3528, "tid": 258, "ts": 1748539272226885, "dur": 634, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 3528, "tid": 258, "ts": 1748539272231497, "dur": 742, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 3528, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266537627, "dur": 18978, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266556607, "dur": 5662122, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266556623, "dur": 39, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266556675, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266556680, "dur": 24327, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266581019, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266581023, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266581074, "dur": 5, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266581081, "dur": 2517, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266583609, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266583615, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266583677, "dur": 2, "ph": "X", "name": "ProcessMessages 1870", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266583680, "dur": 34, "ph": "X", "name": "ReadAsync 1870", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266583718, "dur": 1, "ph": "X", "name": "ProcessMessages 1040", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266583720, "dur": 70, "ph": "X", "name": "ReadAsync 1040", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266583794, "dur": 1, "ph": "X", "name": "ProcessMessages 910", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266583796, "dur": 42, "ph": "X", "name": "ReadAsync 910", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266583840, "dur": 1, "ph": "X", "name": "ProcessMessages 1425", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266583842, "dur": 34, "ph": "X", "name": "ReadAsync 1425", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266583881, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266583916, "dur": 34, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266583955, "dur": 1, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266583958, "dur": 31, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266583992, "dur": 1, "ph": "X", "name": "ProcessMessages 913", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266583993, "dur": 30, "ph": "X", "name": "ReadAsync 913", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266584025, "dur": 33, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266584063, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266584102, "dur": 1, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266584104, "dur": 134, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266584241, "dur": 2, "ph": "X", "name": "ProcessMessages 2246", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266584244, "dur": 41, "ph": "X", "name": "ReadAsync 2246", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266584287, "dur": 1, "ph": "X", "name": "ProcessMessages 920", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266584289, "dur": 30, "ph": "X", "name": "ReadAsync 920", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266584321, "dur": 1, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266584322, "dur": 36, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266584361, "dur": 1, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266584363, "dur": 30, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266584396, "dur": 1, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266584398, "dur": 36, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266584436, "dur": 1, "ph": "X", "name": "ProcessMessages 713", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266584440, "dur": 34, "ph": "X", "name": "ReadAsync 713", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266584476, "dur": 1, "ph": "X", "name": "ProcessMessages 986", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266584478, "dur": 29, "ph": "X", "name": "ReadAsync 986", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266584509, "dur": 21, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266584534, "dur": 93, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266584630, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266584632, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266584681, "dur": 2, "ph": "X", "name": "ProcessMessages 2187", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266584685, "dur": 35, "ph": "X", "name": "ReadAsync 2187", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266584723, "dur": 1, "ph": "X", "name": "ProcessMessages 910", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266584725, "dur": 53, "ph": "X", "name": "ReadAsync 910", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266584782, "dur": 33, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266584817, "dur": 1, "ph": "X", "name": "ProcessMessages 826", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266584820, "dur": 32, "ph": "X", "name": "ReadAsync 826", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266584856, "dur": 1, "ph": "X", "name": "ProcessMessages 854", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266584858, "dur": 97, "ph": "X", "name": "ReadAsync 854", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266584957, "dur": 1, "ph": "X", "name": "ProcessMessages 775", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266584959, "dur": 35, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266584996, "dur": 1, "ph": "X", "name": "ProcessMessages 1877", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266584998, "dur": 31, "ph": "X", "name": "ReadAsync 1877", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585032, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585034, "dur": 31, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585068, "dur": 22, "ph": "X", "name": "ReadAsync 1090", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585092, "dur": 1, "ph": "X", "name": "ProcessMessages 741", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585094, "dur": 30, "ph": "X", "name": "ReadAsync 741", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585126, "dur": 1, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585128, "dur": 31, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585162, "dur": 1, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585164, "dur": 26, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585192, "dur": 1, "ph": "X", "name": "ProcessMessages 684", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585194, "dur": 27, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585223, "dur": 22, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585247, "dur": 1, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585249, "dur": 28, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585280, "dur": 24, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585306, "dur": 18, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585327, "dur": 18, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585348, "dur": 67, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585418, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585471, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585473, "dur": 29, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585505, "dur": 1, "ph": "X", "name": "ProcessMessages 905", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585506, "dur": 85, "ph": "X", "name": "ReadAsync 905", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585595, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585597, "dur": 49, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585649, "dur": 1, "ph": "X", "name": "ProcessMessages 1964", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585652, "dur": 24, "ph": "X", "name": "ReadAsync 1964", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585679, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585680, "dur": 29, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585712, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585714, "dur": 25, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585742, "dur": 20, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585764, "dur": 23, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585790, "dur": 24, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585816, "dur": 20, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585839, "dur": 22, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585863, "dur": 22, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585888, "dur": 20, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585910, "dur": 21, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585934, "dur": 20, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585956, "dur": 18, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585976, "dur": 17, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266585995, "dur": 19, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266586016, "dur": 16, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266586035, "dur": 52, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266586089, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266586114, "dur": 253, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266586372, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266586401, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266586403, "dur": 30, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266586437, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266586439, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266586479, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266586481, "dur": 69, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266586552, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266586555, "dur": 338, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266586896, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266586898, "dur": 56, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266586957, "dur": 4, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266586962, "dur": 105, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266587070, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266587073, "dur": 81, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266587157, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266587161, "dur": 41, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266587205, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266587207, "dur": 14, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266587224, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266587228, "dur": 99, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266587331, "dur": 2, "ph": "X", "name": "ProcessMessages 88", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266587334, "dur": 29, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266587365, "dur": 3, "ph": "X", "name": "ProcessMessages 216", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266587369, "dur": 76, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266587448, "dur": 4, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266587454, "dur": 39, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266587498, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266587533, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266587536, "dur": 153, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266587693, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266587721, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266587723, "dur": 350, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266588077, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266588079, "dur": 190, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266588274, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266588277, "dur": 385, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266588666, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266588668, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266588703, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266588708, "dur": 880, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266589598, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266589603, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266589657, "dur": 34, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266589697, "dur": 34, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266589734, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266589739, "dur": 204, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266589949, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266589991, "dur": 18, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266590010, "dur": 140, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266590156, "dur": 101, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266590261, "dur": 18, "ph": "X", "name": "ProcessMessages 54", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266590281, "dur": 41, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266590325, "dur": 15, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266590342, "dur": 121, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266590471, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266590535, "dur": 10, "ph": "X", "name": "ProcessMessages 54", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266590546, "dur": 885, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266591443, "dur": 32, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266591478, "dur": 1611, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266593098, "dur": 59, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266593159, "dur": 2464, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266595642, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266595647, "dur": 10777, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266606438, "dur": 3, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266606443, "dur": 179, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266606632, "dur": 87, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266606721, "dur": 336725, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266943457, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266943462, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266943499, "dur": 19, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266943519, "dur": 39587, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266983117, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266983123, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266983155, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539266983159, "dur": 19774, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539267002943, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539267002946, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539267002990, "dur": 17, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539267003010, "dur": 744358, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539267747376, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539267747379, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539267747401, "dur": 242, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539267747645, "dur": 35614, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539267783268, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539267783272, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539267783298, "dur": 3, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539267783302, "dur": 1212, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539267784522, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539267784524, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539267784564, "dur": 19, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539267784585, "dur": 20111, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539267804706, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539267804711, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539267804741, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539267804744, "dur": 867, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539267805617, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539267805619, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539267805636, "dur": 17, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539267805654, "dur": 86781, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539267892444, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539267892446, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539267892488, "dur": 17, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539267892506, "dur": 132, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539267892643, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539267892667, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539267892670, "dur": 74181, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539267966865, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539267966870, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539267966909, "dur": 37, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539267966947, "dur": 2608861, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539270575816, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539270575819, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539270575855, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539270575860, "dur": 166428, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539270742294, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539270742298, "dur": 98, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539270742399, "dur": 38, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539270742438, "dur": 33231, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539270775678, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539270775681, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539270775722, "dur": 3, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539270775727, "dur": 792, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539270776523, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539270776550, "dur": 19, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539270776570, "dur": 104508, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539270881089, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539270881094, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539270881120, "dur": 20, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539270881141, "dur": 158, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539270881304, "dur": 78, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539270881385, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539270881387, "dur": 63455, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539270944858, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539270944862, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539270944912, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539270944918, "dur": 842, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539270945763, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539270945766, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539270945798, "dur": 19, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539270945818, "dur": 2205, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539270948031, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539270948034, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539270948092, "dur": 15, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539270948108, "dur": 28917, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539270977033, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539270977036, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539270977074, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539270977078, "dur": 460504, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271437590, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271437593, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271437615, "dur": 36, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271437652, "dur": 34094, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271471754, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271471757, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271471797, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271471800, "dur": 837, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271472642, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271472669, "dur": 17, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271472688, "dur": 72178, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271544876, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271544880, "dur": 123, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271545010, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271545013, "dur": 910, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271545928, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271545959, "dur": 17, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271545976, "dur": 36685, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271582669, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271582671, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271582709, "dur": 21, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271582731, "dur": 152, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271582888, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271582918, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271582920, "dur": 71490, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271654419, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271654423, "dur": 97, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271654523, "dur": 18, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271654542, "dur": 28333, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271682885, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271682889, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271682922, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271682926, "dur": 216820, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271899754, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271899757, "dur": 82, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271899842, "dur": 48, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271899891, "dur": 32555, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271932457, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271932460, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271932496, "dur": 3, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271932501, "dur": 716, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271933223, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271933257, "dur": 15, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539271933275, "dur": 102067, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539272035351, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539272035356, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539272035402, "dur": 19, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539272035421, "dur": 160, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539272035586, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539272035658, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539272035661, "dur": 68462, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539272104130, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539272104133, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539272104204, "dur": 18, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539272104223, "dur": 383, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539272104609, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539272104686, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539272104688, "dur": 61694, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539272166390, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539272166393, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539272166429, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539272166432, "dur": 1134, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539272167571, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539272167573, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539272167595, "dur": 18, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539272167614, "dur": 40910, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539272208532, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539272208536, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539272208599, "dur": 1341, "ph": "X", "name": "ProcessMessages 6910", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539272209947, "dur": 4145, "ph": "X", "name": "ReadAsync 6910", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539272214096, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539272214099, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539272214123, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 3528, "tid": 21474836480, "ts": 1748539272214124, "dur": 4600, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 3528, "tid": 258, "ts": 1748539272232243, "dur": 447, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 3528, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 3528, "tid": 17179869184, "ts": 1748539266537468, "dur": 11, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 3528, "tid": 17179869184, "ts": 1748539266537481, "dur": 19124, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 3528, "tid": 17179869184, "ts": 1748539266556607, "dur": 39, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 3528, "tid": 258, "ts": 1748539272232691, "dur": 8, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 3528, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 3528, "tid": 1, "ts": 1748539264516038, "dur": 4913, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 3528, "tid": 1, "ts": 1748539264520957, "dur": 16621, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 3528, "tid": 1, "ts": 1748539264537586, "dur": 13289, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 3528, "tid": 258, "ts": 1748539272232700, "dur": 3, "ph": "X", "name": "", "args": {}}, {"pid": 3528, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 3528, "tid": 12884901888, "ts": 1748539264513967, "dur": 5465, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748539264519435, "dur": 849615, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748539264520347, "dur": 2574, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748539264522932, "dur": 2132, "ph": "X", "name": "ProcessMessages 20485", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748539264525070, "dur": 207, "ph": "X", "name": "ReadAsync 20485", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748539264525280, "dur": 564, "ph": "X", "name": "ProcessMessages 12635", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748539264525851, "dur": 824060, "ph": "X", "name": "ReadAsync 12635", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748539265349923, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748539265349931, "dur": 133, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748539265350070, "dur": 18050, "ph": "X", "name": "ProcessMessages 6910", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748539265368132, "dur": 118, "ph": "X", "name": "ReadAsync 6910", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748539265368256, "dur": 543, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748539265368805, "dur": 177, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 3528, "tid": 258, "ts": 1748539272232704, "dur": 14, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 3528, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 3528, "tid": 8589934592, "ts": 1748539264511092, "dur": 39849, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 3528, "tid": 8589934592, "ts": 1748539264550944, "dur": 14, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 3528, "tid": 8589934592, "ts": 1748539264550960, "dur": 1446, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 3528, "tid": 258, "ts": 1748539272232720, "dur": 13, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 3528, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 3528, "tid": 4294967296, "ts": 1748539264486553, "dur": 883776, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 3528, "tid": 4294967296, "ts": 1748539264491740, "dur": 13386, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 3528, "tid": 4294967296, "ts": 1748539265370502, "dur": 1163842, "ph": "X", "name": "await ExecuteBuildProgram", "args": {}}, {"pid": 3528, "tid": 4294967296, "ts": 1748539266534547, "dur": 5684219, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 3528, "tid": 4294967296, "ts": 1748539266534666, "dur": 2738, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 3528, "tid": 4294967296, "ts": 1748539272218777, "dur": 6430, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 3528, "tid": 4294967296, "ts": 1748539272221704, "dur": 2110, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 3528, "tid": 4294967296, "ts": 1748539272225213, "dur": 13, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 3528, "tid": 258, "ts": 1748539272232735, "dur": 14, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748539266557896, "dur": 56, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748539266557990, "dur": 25083, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748539266583080, "dur": 1447, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748539266584563, "dur": 347, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748539266585389, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_E022B0A53AAB81FC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748539266586083, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_89B4F204A66FCD3B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748539266586337, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_092BE7AA20DD0577.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748539266584926, "dur": 2554, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748539266587481, "dur": 5626408, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748539272213889, "dur": 313, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748539272214203, "dur": 124, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748539272214333, "dur": 178, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748539272215468, "dur": 1100, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748539266585273, "dur": 2255, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748539266587544, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748539266587602, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1748539266587532, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_D60F4678B0C9138A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748539266587829, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748539266587828, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_DC8E3421511E8CD6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748539266587886, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748539266587946, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748539266587944, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_FFCF669E316445AF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748539266588278, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_5758047E8BFC2F0C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748539266588438, "dur": 180, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748539266588437, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_EBC2945A5C8EC5BE.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748539266588621, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748539266597981, "dur": 8859, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748539266589640, "dur": 17214, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748539266606856, "dur": 1177587, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748539267784443, "dur": 2992446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748539270776889, "dur": 695991, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748539271472882, "dur": 197, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748539271472881, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748539271473093, "dur": 918, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748539271474014, "dur": 459581, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748539271933596, "dur": 280277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748539266585503, "dur": 2087, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748539266587649, "dur": 170, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1748539266587593, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_2774BB72C3E9800F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748539266587820, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748539266587883, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748539266587882, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_0954F19AD29BDF65.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748539266587952, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748539266588144, "dur": 595, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748539266589640, "dur": 2294, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748539266605605, "dur": 1313, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748539266591973, "dur": 14954, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748539266606929, "dur": 1177517, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748539267784446, "dur": 2992420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748539270776892, "dur": 130, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1748539270776891, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1748539270777030, "dur": 876, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1748539270777909, "dur": 694996, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748539271472906, "dur": 460690, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748539271933596, "dur": 280279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748539266585261, "dur": 2255, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748539266587538, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748539266587600, "dur": 207, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1748539266587527, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_052C350FFD3D9DA1.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748539266587808, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748539266587881, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748539266587880, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_94F18CE7015FE34E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748539266587982, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748539266587981, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_5AF75CBDC7D22D6F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748539266588125, "dur": 466, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748539266589717, "dur": 17952, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748539266607672, "dur": 1176762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748539267784434, "dur": 2992458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748539270776892, "dur": 696010, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748539271472903, "dur": 460703, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748539271933607, "dur": 280298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748539266585224, "dur": 2278, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748539266587513, "dur": 2497, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748539266603499, "dur": 3404, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748539266590359, "dur": 16559, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748539266606921, "dur": 1177530, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748539267784452, "dur": 2992445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748539270776898, "dur": 696001, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748539271472901, "dur": 460704, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748539271933605, "dur": 280286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748539266585559, "dur": 2054, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748539266587630, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748539266587687, "dur": 188, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1748539266587617, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_5B115C4D92515B9E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748539266587876, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748539266587941, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_5985A353A58EF3A3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748539266588317, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_1882500C00DD15DD.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748539266588514, "dur": 136, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748539266588512, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7574F99E59035FF8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748539266589572, "dur": 2857, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1748539266592432, "dur": 801, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748539266593233, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748539266593917, "dur": 1098, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748539266595016, "dur": 2515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748539266597532, "dur": 1059, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748539266598592, "dur": 618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748539266599211, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748539266599833, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748539266600400, "dur": 628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748539266601029, "dur": 609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748539266601639, "dur": 731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748539266602416, "dur": 1182015, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748539267784432, "dur": 2992462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748539270776894, "dur": 695984, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748539271472880, "dur": 73252, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TextMeshPro.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748539271472879, "dur": 73255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748539271546172, "dur": 1099, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TextMeshPro.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748539271547273, "dur": 386319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748539271933592, "dur": 280295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748539266585420, "dur": 2136, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748539266587618, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1748539266587566, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_958DBB398EA19D56.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748539266587724, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748539266587824, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748539266587892, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748539266587891, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_15AAF2E558C07C16.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748539266588039, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748539266588230, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_7D7229C02E0DB889.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748539266588499, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748539266589675, "dur": 17773, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748539266607451, "dur": 1176988, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748539267784440, "dur": 2992450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748539270776891, "dur": 696010, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748539271472902, "dur": 460711, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748539271933613, "dur": 280277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748539266585346, "dur": 2190, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748539266587606, "dur": 158, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1748539266587539, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_FB0CA54B74E90E14.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748539266587810, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_169242F115DD75D8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748539266587878, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748539266588058, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748539266588315, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_46987855B0E3A6A0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748539266588429, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748539266588627, "dur": 212, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.NVIDIAModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748539266588626, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_B799F8A1111E0279.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748539266589322, "dur": 414964, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_B799F8A1111E0279.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748539267004348, "dur": 780081, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748539267784430, "dur": 2992465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748539270776895, "dur": 695994, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748539271472891, "dur": 460694, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748539271933589, "dur": 234118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TextMeshPro.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748539271933588, "dur": 234121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748539272167724, "dur": 1214, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748539272168943, "dur": 44963, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748539266585455, "dur": 2117, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748539266587585, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748539266587651, "dur": 176, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 8, "ts": 1748539266587576, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_E660793899F04DE4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748539266587875, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748539266587874, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_431E85A617E2A342.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748539266588350, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_43B074E6D83CA464.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748539266588531, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748539266588530, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_690369FE7ACF6B74.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748539266589622, "dur": 2204, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748539266603386, "dur": 3503, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748539266591867, "dur": 15029, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748539266606899, "dur": 1177536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748539267784435, "dur": 2992472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748539270776907, "dur": 695997, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748539271472905, "dur": 460702, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748539271933608, "dur": 280268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748539266585494, "dur": 2087, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748539266587596, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748539266587671, "dur": 172, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": 1748539266587585, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_6C261F44AB06F2F8.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748539266587890, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748539266587888, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_A4331CFC950ED6D5.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748539266587951, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748539266588326, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_040BEB3B4DCBE197.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748539266588526, "dur": 150, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748539266588524, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_043AF99BA411347E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748539266589633, "dur": 2371, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1748539266592006, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748539266592616, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748539266593292, "dur": 843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748539266594136, "dur": 559, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748539266595060, "dur": 713, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748539266594136, "dur": 3176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748539266597313, "dur": 2069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748539266599382, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748539266600140, "dur": 715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748539266600856, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748539266601539, "dur": 905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748539266602445, "dur": 1181996, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748539267784441, "dur": 2992461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748539270776902, "dur": 695990, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748539271472893, "dur": 460716, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748539271933609, "dur": 280275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748539266585583, "dur": 2038, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748539266587681, "dur": 170, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 10, "ts": 1748539266587624, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_132925DC02CDA280.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748539266587852, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748539266587958, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748539266588058, "dur": 412, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748539266588475, "dur": 146, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748539266588474, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_E022B0A53AAB81FC.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748539266594255, "dur": 11531, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748539266589928, "dur": 15882, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1748539266605816, "dur": 1178611, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748539267784429, "dur": 186, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1748539267784429, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1748539267784651, "dur": 1238, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1748539267785891, "dur": 2990990, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748539270776882, "dur": 696003, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748539271472885, "dur": 460703, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748539271933592, "dur": 175, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1748539271933591, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1748539271933784, "dur": 806, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1748539271934591, "dur": 279316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748539266585523, "dur": 2079, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748539266587619, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748539266587681, "dur": 197, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 11, "ts": 1748539266587606, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_7CC3BC782BC99F3E.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748539266587880, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748539266588307, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_06BA14BCF0F515E9.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748539266588369, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GIModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748539266588368, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_2DCA04472A508570.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748539266588519, "dur": 190, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748539266588518, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_4F00803CEBBEFC1F.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748539266589653, "dur": 2425, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1748539266592083, "dur": 1706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748539266593790, "dur": 715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748539266594505, "dur": 3282, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748539266597787, "dur": 2502, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748539266600395, "dur": 3941, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748539266594505, "dur": 11819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748539266606325, "dur": 1178108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748539267784433, "dur": 2992470, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748539270776904, "dur": 696003, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748539271472907, "dur": 460686, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748539271933594, "dur": 280297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748539266585606, "dur": 2023, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748539266587633, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_4639AD57CB8F0BF2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748539266587869, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748539266587940, "dur": 414, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityCurlModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748539266587938, "dur": 417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_092BE7AA20DD0577.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748539266588405, "dur": 210, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748539266588404, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_8D1DAC88172B6825.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748539266588618, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748539266589460, "dur": 2060, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1748539266596547, "dur": 10240, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748539266591533, "dur": 15272, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1748539266606808, "dur": 1177634, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748539267784442, "dur": 2992462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748539270776905, "dur": 695998, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748539271472904, "dur": 460694, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748539271933598, "dur": 280306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748539266585634, "dur": 2004, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748539266587642, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_B3BB0DA1997A2647.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748539266587707, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748539266587882, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748539266587937, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityTestProtocolModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539266587936, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_E33771651DE4D565.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748539266588229, "dur": 192, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_22B5366951C60B1F.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748539266589553, "dur": 1699, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1748539266591350, "dur": 15850, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1748539266591258, "dur": 15943, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748539266607220, "dur": 337540, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748539266945022, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventData\\AxisEventData.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266946258, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\ColorBlock.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266946434, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\RectangularVertexClipper.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266946486, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\DefaultControls.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266946563, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Dropdown.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266946648, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\FontData.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266946702, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\FontUpdateTracker.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266946754, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Graphic.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266946816, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\GraphicRaycaster.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266946916, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\GraphicRegistry.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266946970, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\IGraphicEnabledDisabled.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266947025, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Image.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266947120, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\IMask.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266947179, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\IMaskable.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266947238, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\InputField.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266947373, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\AspectRatioFitter.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266947446, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\CanvasScaler.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266947510, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\ContentSizeFitter.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266947561, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\GridLayoutGroup.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266947618, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\HorizontalLayoutGroup.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266947669, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\HorizontalOrVerticalLayoutGroup.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266947733, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\ILayoutElement.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266947792, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\LayoutElement.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266947847, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\LayoutGroup.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266947903, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\LayoutRebuilder.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266947964, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\LayoutUtility.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266948022, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\VerticalLayoutGroup.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266948073, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Mask.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266948129, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\MaskableGraphic.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266948185, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\MaskUtilities.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266948244, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\MaterialModifiers\\IMaterialModifier.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266948301, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Misc.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266948353, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\MultipleDisplayUtilities.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266948405, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Navigation.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266948461, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\RawImage.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266948512, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\RectMask2D.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266948565, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Scrollbar.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266948624, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\ScrollRect.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266948711, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Selectable.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266948790, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\SetPropertyUtility.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266948849, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Slider.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266948920, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\SpecializedCollections\\IndexedSet.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266948980, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\SpriteState.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266949037, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\StencilMaterial.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266949098, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Text.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266949171, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Toggle.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266949235, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\ToggleGroup.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266949304, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Utility\\ReflectionMethodsCache.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266949368, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Utility\\VertexHelper.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266949433, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\VertexModifiers\\BaseMeshEffect.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266949489, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\VertexModifiers\\IMeshModifier.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266949547, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\VertexModifiers\\Outline.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266949607, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\VertexModifiers\\PositionAsUV1.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266949661, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\VertexModifiers\\Shadow.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539266949721, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1748539266944790, "dur": 5046, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1748539266949844, "dur": 34457, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539266984313, "dur": 89, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748539267748962, "dur": 29947, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539266984420, "dur": 794501, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1748539267784427, "dur": 162, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.UI.ref.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539267784425, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_08FEAA520A2EFD60.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748539267784607, "dur": 109168, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_08FEAA520A2EFD60.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748539267893906, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1748539267893809, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748539267894020, "dur": 74166, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748539267968378, "dur": 20215, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539267988595, "dur": 33084, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\netstandard.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268021682, "dur": 19418, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.AppContext.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268041103, "dur": 31180, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Buffers.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268072291, "dur": 21136, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268093434, "dur": 20952, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268114389, "dur": 18936, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268133327, "dur": 19127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268152455, "dur": 22300, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Annotations.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268174757, "dur": 18448, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268193207, "dur": 20144, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268213354, "dur": 18099, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268231455, "dur": 18920, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268250376, "dur": 21679, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Console.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268272057, "dur": 17998, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268290057, "dur": 17718, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268307778, "dur": 19492, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268327272, "dur": 18897, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268346171, "dur": 20927, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268367100, "dur": 20692, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268387794, "dur": 21680, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268409476, "dur": 21299, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268430779, "dur": 21573, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268452355, "dur": 19511, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268471875, "dur": 21967, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268493845, "dur": 19516, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268513364, "dur": 20264, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268533630, "dur": 19248, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268552880, "dur": 20315, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268573204, "dur": 19733, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268592940, "dur": 23403, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268616346, "dur": 24933, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268641283, "dur": 26456, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268667742, "dur": 20294, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268688039, "dur": 19996, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268708038, "dur": 19508, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268727548, "dur": 23137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268750688, "dur": 22448, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268773139, "dur": 20508, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268793649, "dur": 24453, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268818104, "dur": 20261, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268838367, "dur": 21900, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268860269, "dur": 19865, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Memory.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268880137, "dur": 18998, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Http.Rtc.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268899138, "dur": 19542, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268918682, "dur": 20330, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268939015, "dur": 28068, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268967085, "dur": 22998, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539268990085, "dur": 27004, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269017092, "dur": 25089, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269042184, "dur": 19125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269061311, "dur": 19964, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269081277, "dur": 19053, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269100332, "dur": 18482, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269118815, "dur": 21300, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269140120, "dur": 17919, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269158041, "dur": 20852, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269178895, "dur": 21195, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269200093, "dur": 20842, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269220938, "dur": 18814, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269239755, "dur": 18611, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269258368, "dur": 19031, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269277401, "dur": 20628, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269298031, "dur": 18052, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269316085, "dur": 17577, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269333665, "dur": 19285, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269352952, "dur": 20299, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269373252, "dur": 19354, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269392609, "dur": 18784, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269411395, "dur": 19029, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269430426, "dur": 18593, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269449022, "dur": 17583, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269466607, "dur": 18709, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269485319, "dur": 18343, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269503664, "dur": 17945, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269521612, "dur": 18505, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269540120, "dur": 17992, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269558115, "dur": 19021, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269577139, "dur": 20649, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Algorithms.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269597790, "dur": 18481, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269616276, "dur": 19001, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269635280, "dur": 19528, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269654811, "dur": 19289, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269674102, "dur": 18289, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269692394, "dur": 18037, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269710434, "dur": 18768, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Duplex.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269729204, "dur": 20065, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Http.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269749271, "dur": 17409, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.NetTcp.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269766682, "dur": 18803, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Primitives.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269785487, "dur": 18349, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Security.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269803838, "dur": 18440, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269822282, "dur": 18520, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269840804, "dur": 18461, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269859268, "dur": 17925, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269877196, "dur": 17202, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269894400, "dur": 18225, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269912628, "dur": 17541, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269930180, "dur": 18738, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269948920, "dur": 16876, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269965797, "dur": 17471, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539269983270, "dur": 17643, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539270000916, "dur": 17390, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539270018308, "dur": 17189, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539270035499, "dur": 18184, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539270053685, "dur": 16990, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539270070680, "dur": 18343, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539270089025, "dur": 17168, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539270106195, "dur": 17757, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539270123955, "dur": 18870, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Microsoft.CSharp.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539270142828, "dur": 64455, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\mscorlib.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539270207285, "dur": 22239, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539270229526, "dur": 29098, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Core.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539270258625, "dur": 18113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.DataSetExtensions.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539270276740, "dur": 26408, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539270303150, "dur": 37860, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539270341013, "dur": 28352, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Drawing.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539270369366, "dur": 19888, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539270389257, "dur": 17763, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539270407023, "dur": 21022, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539270428047, "dur": 17948, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Numerics.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539270445997, "dur": 18714, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539270464712, "dur": 20773, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539270485486, "dur": 19995, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Transactions.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539270505482, "dur": 23454, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Xml.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539270528938, "dur": 20582, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539270549523, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\EventSystem\\EventSystemEditor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270549623, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\EventSystem\\EventTriggerEditor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270549691, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\EventSystem\\Physics2DRaycasterEditor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270549749, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\EventSystem\\PhysicsRaycasterEditor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270549824, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\Properties\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270549886, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\AspectRatioFitterEditor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270549944, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\ButtonEditor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270550013, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\CanvasScalerEditor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270550077, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\ContentSizeFitterEditor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270550129, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\DropdownEditor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270550184, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\GraphicEditor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270550243, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\GridLayoutGroupEditor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270550299, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\HorizontalOrVerticalLayoutGroupEditor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270550354, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\ImageEditor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270550406, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\InputFieldEditor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270550472, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\InterceptedEventsPreview.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270550531, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\LayoutElementEditor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270550586, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\LayoutPropertiesPreview.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270550709, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\MenuOptions.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270550761, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\PrefabLayoutRebuilder.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270550812, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\PropertyDrawers\\AnimationTriggersDrawer.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270550866, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\PropertyDrawers\\ColorBlockDrawer.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270550919, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\PropertyDrawers\\DropdownOptionListDrawer.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270550974, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\PropertyDrawers\\FontDataDrawer.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270551055, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\PropertyDrawers\\NavigationDrawer.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270551118, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\PropertyDrawers\\SpriteStateDrawer.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270551169, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\RawImageEditor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270551230, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\RectMask2DEditor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270551283, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\ScrollbarEditor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270551339, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\ScrollRectEditor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270551406, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\SelectableEditor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270551480, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\SelfControllerEditor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270551550, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\SliderEditor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270551612, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\SpriteDrawUtility.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270551669, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\TextEditor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270551739, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\ToggleEditor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270551817, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1748539267968235, "dur": 2583693, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1748539270551933, "dur": 25140, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539270577079, "dur": 55, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748539270743831, "dur": 28504, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539270577144, "dur": 195201, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1748539270776864, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.UI.ref.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539270776862, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_4C98D3F7040CD4F5.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748539270777020, "dur": 105341, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_4C98D3F7040CD4F5.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748539270882473, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1748539270882394, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748539270882609, "dur": 66780, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748539270949621, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270949726, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\FastAction.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270949796, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\ITextPreProcessor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270949859, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\MaterialReferenceManager.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270949934, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TextContainer.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270950001, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TextMeshPro.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270950068, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TextMeshProUGUI.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270950147, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMPro_EventManager.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270950211, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMPro_ExtensionMethods.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270950277, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMPro_MeshUtilities.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270950342, "dur": 167, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMPro_Private.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270950510, "dur": 162, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMPro_UGUI_Private.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270950673, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_Asset.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270950732, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_Character.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270950788, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_CharacterInfo.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270950846, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_ColorGradient.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270950912, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_Compatibility.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270950969, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_CoroutineTween.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270951028, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_DefaultControls.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270951092, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_Dropdown.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270951168, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_EditorResourceManager.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270951227, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_FontAsset.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270951345, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_FontAssetCommon.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270951411, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_FontAssetUtilities.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270951484, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_FontFeaturesCommon.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270951544, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_FontFeatureTable.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270951599, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_InputField.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270951720, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_InputValidator.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270951776, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_LineInfo.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270951830, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_ListPool.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270951883, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_MaterialManager.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270951949, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_MeshInfo.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270952014, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_ObjectPool.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270952069, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_PackageResourceImporter.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270952128, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_ResourcesManager.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270952186, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_RichTextTagsCommon.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270952254, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_ScrollbarEventHandler.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270952325, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_SelectionCaret.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270952382, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_Settings.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270952448, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_ShaderUtilities.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270952515, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_Sprite.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270952572, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_SpriteAnimator.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270952638, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_SpriteAsset.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270952701, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_SpriteAssetImportFormats.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270952758, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_SpriteCharacter.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270952815, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_SpriteGlyph.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270952868, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_Style.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270952923, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_StyleSheet.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270952976, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_SubMesh.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270953038, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_SubMeshUI.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270953103, "dur": 232, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_Text.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270953336, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_TextElement.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270953391, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_TextElement_Legacy.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270953446, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_TextInfo.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270953503, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_TextParsingUtilities.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270953559, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_TextProcessingStack.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270953620, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_TextUtilities.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270953716, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_UpdateManager.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270953790, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_UpdateRegistery.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539270953848, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1748539270949419, "dur": 4517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1748539270953942, "dur": 24357, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TextMeshPro.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539270978307, "dur": 64, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748539271439480, "dur": 28193, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TextMeshPro.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539270978382, "dur": 489299, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1748539271472880, "dur": 187, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TextMeshPro.ref.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539271472878, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_84A4293DBDD182DB.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748539271473091, "dur": 110927, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_84A4293DBDD182DB.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748539271584135, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1748539271584054, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748539271584261, "dur": 71507, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748539271656025, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\DropdownOptionListDrawer.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271656154, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\GlyphInfoDrawer.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271656232, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\GlyphMetricsPropertyDrawer.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271656298, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\GlyphRectPropertyDrawer.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271656363, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMPro_ContextMenus.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271656428, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMPro_CreateObjectMenu.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271656498, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMPro_EditorShaderUtilities.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271656559, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMPro_FontAssetCreatorWindow.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271656671, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMPro_FontPlugin.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271656729, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMPro_SortingLayerHelper.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271656788, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMPro_TextContainerEditor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271656848, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMPro_TexturePostProcessor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271656906, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_BaseEditorPanel.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271656987, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_BaseShaderGUI.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271657050, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_BitmapShaderGUI.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271657107, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_CharacterPropertyDrawer.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271657170, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_ColorGradientAssetMenu.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271657228, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_ColorGradientEditor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271657290, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_DropdownEditor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271657345, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_EditorCoroutine.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271657401, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_EditorPanel.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271657459, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_EditorPanelUI.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271657515, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_EditorUtility.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271657577, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_FontAssetEditor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271657667, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_FontAsset_CreationMenu.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271657729, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_GlyphPairAdjustmentRecordPropertyDrawer.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271657796, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_GlyphPropertyDrawer.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271657855, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_InputFieldEditor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271657918, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_MeshRendererEditor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271657974, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_PackageUtilities.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271658050, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_PostBuildProcessHandler.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271658106, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_PreBuildProcessor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271658167, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_ProjectTextSettings.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271658226, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_ResourcesLoader.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271658283, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_SDFShaderGUI.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271658347, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_SerializedPropertyHolder.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271658402, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_SettingsEditor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271658467, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_SpriteAssetEditor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271658541, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_SpriteAssetImporter.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271658601, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_SpriteAssetMenu.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271658662, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_SpriteCharacterPropertyDrawer.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271658726, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_SpriteGlyphPropertyDrawer.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271658784, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_StyleAssetMenu.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271658861, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_StyleSheetEditor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271658922, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_SubMeshUI_Editor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271658979, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_SubMesh_Editor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271659036, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_TextAlignmentDrawer.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271659096, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_UIStyleManager.cs"}}, {"pid": 12345, "tid": 13, "ts": 1748539271659174, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1748539271655803, "dur": 3468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1748539271659279, "dur": 24862, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TextMeshPro.Editor.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539271684148, "dur": 61, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748539271901355, "dur": 27459, "ph": "X", "name": "InvokeCacheMe", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TextMeshPro.Editor.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539271684224, "dur": 244610, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1748539271933589, "dur": 182, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.TextMeshPro.Editor.ref.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539271933586, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_159E061D77A10B86.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748539271933787, "dur": 102872, "ph": "X", "name": "MovedFromExtractor", "args": {"detail": "Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_159E061D77A10B86.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748539272036780, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1748539272036696, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748539272036904, "dur": 68580, "ph": "X", "name": "MovedFromExtractorCombine", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748539272105731, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1748539272105520, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1748539272105907, "dur": 103963, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1748539266585737, "dur": 1911, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748539266587651, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_318BC6ACF69CAB06.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748539266587716, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748539266589722, "dur": 3619, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1748539266593347, "dur": 804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748539266594152, "dur": 1334, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 14, "ts": 1748539266595487, "dur": 1118, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 14, "ts": 1748539266597597, "dur": 778, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 14, "ts": 1748539266598377, "dur": 9114, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 14, "ts": 1748539266594151, "dur": 14134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748539266608286, "dur": 1176150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748539267784436, "dur": 2992435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748539270776879, "dur": 169271, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 14, "ts": 1748539270776878, "dur": 169274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 14, "ts": 1748539270946172, "dur": 962, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 14, "ts": 1748539270947137, "dur": 525760, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748539271472897, "dur": 460705, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748539271933602, "dur": 280278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748539266585758, "dur": 1897, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748539266587658, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_8ED626F90EA59802.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1748539266587717, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748539266587890, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1748539266587889, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_C9CFE1DD70D57119.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1748539266588064, "dur": 415, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1748539266588062, "dur": 420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_7933C8582AE17C92.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1748539266588482, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748539266589553, "dur": 1427, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1748539266591022, "dur": 16642, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1748539266607666, "dur": 1176771, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748539267784437, "dur": 2992461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748539270776899, "dur": 695999, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748539271472899, "dur": 460701, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748539271933600, "dur": 280277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748539266585795, "dur": 1868, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748539266587675, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1748539266587664, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_C2F00331D8C496E9.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1748539266589580, "dur": 2068, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1748539266603177, "dur": 3691, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748539266591662, "dur": 15213, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1748539266606876, "dur": 1177549, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748539267784430, "dur": 21585, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 16, "ts": 1748539267784429, "dur": 21588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 16, "ts": 1748539267806035, "dur": 950, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 16, "ts": 1748539267806988, "dur": 2969896, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748539270776884, "dur": 695998, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748539271472883, "dur": 460707, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748539271933591, "dur": 280280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748539272218041, "dur": 1726, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "netcorerun.dll"}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 35942, "tid": 1, "ts": 1748539266381943, "dur": 135095, "ph": "X", "name": "BuildProgram", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1748539266383042, "dur": 50526, "ph": "X", "name": "BuildProgramContextConstructor", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1748539266495172, "dur": 5171, "ph": "X", "name": "OutputData.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1748539266500349, "dur": 16674, "ph": "X", "name": "Backend.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1748539266501641, "dur": 13890, "ph": "X", "name": "JsonToString", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1748539266524887, "dur": 1368, "ph": "X", "name": "", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1748539266523973, "dur": 2542, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748539264517966, "dur": 993, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748539264518969, "dur": 63, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748539264519060, "dur": 370, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748539264520114, "dur": 3224, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_E0AA9EA79E7FEE40.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748539264519450, "dur": 4334, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748539264523785, "dur": 30413, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748539264554199, "dur": 215, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748539264554414, "dur": 64, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748539264554527, "dur": 801447, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748539265358427, "dur": 1709, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748539264519614, "dur": 4204, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748539264523835, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748539264523893, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1748539264523822, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_926D2B195BEC1C1F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748539264524029, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748539264524093, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748539264524092, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_690369FE7ACF6B74.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748539264524220, "dur": 359, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_690369FE7ACF6B74.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748539264524580, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_4639AD57CB8F0BF2.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748539264524633, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748539264524837, "dur": 29368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748539264519581, "dur": 4212, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748539264523817, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748539264523887, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1748539264523806, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_0954F19AD29BDF65.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748539264524110, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748539264524108, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_91015966EAE8A302.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748539264524218, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_91015966EAE8A302.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748539264524511, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748539264524510, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7574F99E59035FF8.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748539264524620, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748539264524618, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_C2F00331D8C496E9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748539264524727, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748539264524846, "dur": 29345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748539264519619, "dur": 4212, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748539264523844, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748539264523945, "dur": 142, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1748539264523835, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_431E85A617E2A342.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748539264524134, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748539264524133, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_899F220464CF5AC5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748539264524354, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PhysicsModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748539264524352, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_6F6E7695D0B1844D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748539264524474, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748539264524472, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_0E8B52DF278607E5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748539264524574, "dur": 229, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748539264524573, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_132925DC02CDA280.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748539264524839, "dur": 29357, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748539264519605, "dur": 4203, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748539264523820, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748539264523892, "dur": 234, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": 1748539264523811, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_A67C459F9DC5AEAD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748539264524128, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748539264524228, "dur": 168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_A67C459F9DC5AEAD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748539264524399, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748539264524397, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_06BA14BCF0F515E9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748539264524460, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748539264524639, "dur": 194, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748539264524638, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_901B9CE39A8D7C0F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748539264524873, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/RichBillionaire.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748539264525087, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\mscorlib.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748539264525143, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748539264525493, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748539264524969, "dur": 627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RichBillionaire.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748539264526060, "dur": 824991, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/RichBillionaire.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748539264519656, "dur": 4193, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748539264523904, "dur": 277, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1748539264523852, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_169242F115DD75D8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748539264524183, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748539264524465, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748539264524653, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748539264524856, "dur": 29361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748539264519649, "dur": 4191, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748539264523853, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748539264523906, "dur": 282, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1748539264523844, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_043AF99BA411347E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748539264524233, "dur": 168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_043AF99BA411347E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748539264524640, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748539264524639, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_2C6DDFD7B4B5E2B3.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748539264524798, "dur": 28742, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748539264553541, "dur": 625, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748539264519684, "dur": 4170, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748539264523915, "dur": 175, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1748539264523857, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_DC8E3421511E8CD6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748539264524092, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748539264524180, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_DC8E3421511E8CD6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748539264524641, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748539264524733, "dur": 812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748539264525545, "dur": 28654, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748539264519713, "dur": 4149, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748539264523876, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748539264523945, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 8, "ts": 1748539264523868, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_E3D37FEC8CA3287A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748539264524081, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748539264524177, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_D970D7A0A54F1A69.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748539264524519, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748539264524518, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_4F00803CEBBEFC1F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748539264524605, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748539264524733, "dur": 666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748539264525400, "dur": 28792, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748539264519740, "dur": 4131, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748539264523882, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748539264523941, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": 1748539264523874, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_95E784EC8E0862CB.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748539264524083, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748539264524175, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_95E784EC8E0862CB.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748539264524240, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748539264524239, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_9188A12F15CB614B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748539264524352, "dur": 199, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_9188A12F15CB614B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748539264524553, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748539264524552, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_6C261F44AB06F2F8.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748539264524646, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748539264524740, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748539264525422, "dur": 28773, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748539264519760, "dur": 4160, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748539264523930, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748539264523922, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_052C350FFD3D9DA1.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748539264524116, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748539264524115, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_1412F4782AF279E1.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748539264524321, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ScreenCaptureModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748539264524320, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_605FAFF9AD97DAD0.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748539264524582, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_B3BB0DA1997A2647.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748539264524634, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748539264524723, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_B3BB0DA1997A2647.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748539264524808, "dur": 29398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748539264519787, "dur": 4141, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748539264524114, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748539264524112, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_7D7229C02E0DB889.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748539264524227, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_7D7229C02E0DB889.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748539264524497, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748539264524651, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748539264524736, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748539264525385, "dur": 28830, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748539264519810, "dur": 4130, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748539264523950, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748539264523943, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_5AF75CBDC7D22D6F.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748539264524130, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_28B15985BE2CAE44.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748539264524220, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_28B15985BE2CAE44.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748539264524451, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_5F1226B013296F98.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748539264524629, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748539264524628, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_0789CA4FD66C3EE4.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748539264524801, "dur": 29392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748539264519839, "dur": 4111, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748539264524047, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539264524046, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_5985A353A58EF3A3.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748539264524176, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748539264524226, "dur": 145, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_5985A353A58EF3A3.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748539264524453, "dur": 118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DirectorModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748539264524452, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_9191F81B58720FCA.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748539264524574, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748539264524847, "dur": 29355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748539264519860, "dur": 4103, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748539264523975, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1748539264523966, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_C9CFE1DD70D57119.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748539264524124, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748539264524189, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_7CF00AAB3B07EE8A.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748539264524523, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1748539264524522, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_FB0CA54B74E90E14.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748539264524607, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748539264524729, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748539264525406, "dur": 28803, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748539264519888, "dur": 4081, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748539264523972, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_15AAF2E558C07C16.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1748539264524028, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748539264524201, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_15AAF2E558C07C16.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1748539264524456, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1748539264524454, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_8D1DAC88172B6825.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1748539264524548, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1748539264524547, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_958DBB398EA19D56.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1748539264524619, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748539264524727, "dur": 740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748539264525467, "dur": 28748, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748539264519923, "dur": 4053, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748539264523977, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_59ADA145A78A1AC4.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1748539264524036, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748539264524183, "dur": 303, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_59ADA145A78A1AC4.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1748539264524657, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748539264524750, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748539264525330, "dur": 28878, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748539265361847, "dur": 804, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 3528, "tid": 258, "ts": 1748539272233177, "dur": 1870, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend2.traceevents"}}, {"pid": 3528, "tid": 258, "ts": 1748539272237096, "dur": 34, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"}}, {"pid": 3528, "tid": 258, "ts": 1748539272237337, "dur": 15, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 3528, "tid": 258, "ts": 1748539272235121, "dur": 1971, "ph": "X", "name": "backend2.traceevents", "args": {}}, {"pid": 3528, "tid": 258, "ts": 1748539272237182, "dur": 154, "ph": "X", "name": "buildprogram0.traceevents", "args": {}}, {"pid": 3528, "tid": 258, "ts": 1748539272237385, "dur": 256, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 3528, "tid": 258, "ts": 1748539272229810, "dur": 8610, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}