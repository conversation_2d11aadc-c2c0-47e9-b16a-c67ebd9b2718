{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 3528, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 3528, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 3528, "tid": 290, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 3528, "tid": 290, "ts": 1748540864771466, "dur": 9, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 3528, "tid": 290, "ts": 1748540864771490, "dur": 5, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 3528, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 3528, "tid": 1, "ts": 1748540864580457, "dur": 823, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 3528, "tid": 1, "ts": 1748540864581281, "dur": 7426, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 3528, "tid": 1, "ts": 1748540864588709, "dur": 10864, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 3528, "tid": 290, "ts": 1748540864771497, "dur": 7, "ph": "X", "name": "", "args": {}}, {"pid": 3528, "tid": 60129542144, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864580427, "dur": 12991, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864593420, "dur": 177529, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864593427, "dur": 1493, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864595156, "dur": 109, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864595356, "dur": 303, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864595960, "dur": 9, "ph": "X", "name": "ProcessMessages 19411", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864596082, "dur": 331, "ph": "X", "name": "ReadAsync 19411", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864596415, "dur": 7, "ph": "X", "name": "ProcessMessages 14288", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864596423, "dur": 2531, "ph": "X", "name": "ReadAsync 14288", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864598958, "dur": 7, "ph": "X", "name": "ProcessMessages 4968", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864598967, "dur": 156, "ph": "X", "name": "ReadAsync 4968", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864599126, "dur": 11, "ph": "X", "name": "ProcessMessages 4629", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864599138, "dur": 430, "ph": "X", "name": "ReadAsync 4629", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864599574, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864599598, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864599658, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864599686, "dur": 18, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864599707, "dur": 307, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864600018, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864600042, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864600065, "dur": 53, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864600122, "dur": 260, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864600386, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864600418, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864600439, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864600462, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864600499, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864600515, "dur": 245, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864600764, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864600820, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864600841, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864600843, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864600887, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864600934, "dur": 572, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864601509, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864601531, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864601533, "dur": 158669, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864760210, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864760214, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864760272, "dur": 2306, "ph": "X", "name": "ProcessMessages 4781", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864762581, "dur": 5253, "ph": "X", "name": "ReadAsync 4781", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864767840, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864767843, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864767884, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 3528, "tid": 60129542144, "ts": 1748540864767886, "dur": 3058, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 3528, "tid": 290, "ts": 1748540864771505, "dur": 116, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 3528, "tid": 55834574848, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 3528, "tid": 55834574848, "ts": 1748540864580393, "dur": 19189, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 3528, "tid": 55834574848, "ts": 1748540864599584, "dur": 21, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 3528, "tid": 290, "ts": 1748540864771627, "dur": 6, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 3528, "tid": 51539607552, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 3528, "tid": 51539607552, "ts": 1748540864577636, "dur": 193341, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 3528, "tid": 51539607552, "ts": 1748540864577760, "dur": 2589, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 3528, "tid": 51539607552, "ts": 1748540864770979, "dur": 39, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 3528, "tid": 51539607552, "ts": 1748540864770993, "dur": 12, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 3528, "tid": 51539607552, "ts": 1748540864771020, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 3528, "tid": 290, "ts": 1748540864771635, "dur": 9, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748540864593845, "dur": 988, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748540864594842, "dur": 80, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748540864594950, "dur": 368, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748540864595417, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_A67C459F9DC5AEAD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748540864596180, "dur": 212, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_7933C8582AE17C92.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748540864596943, "dur": 145, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_84A4293DBDD182DB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748540864597278, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748540864595343, "dur": 2118, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748540864597462, "dur": 167454, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748540864764929, "dur": 342, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748540864765296, "dur": 111, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748540864768635, "dur": 1032, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748540864595754, "dur": 1736, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748540864597547, "dur": 158, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1748540864597494, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_958DBB398EA19D56.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748540864598127, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748540864598240, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748540864598301, "dur": 1116, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748540864599417, "dur": 773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748540864600190, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748540864600399, "dur": 454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748540864600853, "dur": 356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748540864601210, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748540864601632, "dur": 163312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748540864595688, "dur": 1796, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748540864597495, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748540864597547, "dur": 186, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1748540864597487, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_FB0CA54B74E90E14.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748540864597735, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748540864597804, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748540864597803, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_E2964BE09A8A93C0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748540864597858, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748540864598109, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748540864598228, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748540864598291, "dur": 1013, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748540864599305, "dur": 789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748540864600095, "dur": 287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748540864600433, "dur": 378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748540864600813, "dur": 390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748540864601203, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748540864601347, "dur": 245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748540864601592, "dur": 163332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748540864595888, "dur": 1679, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748540864597625, "dur": 146, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1748540864597571, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_2774BB72C3E9800F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748540864597809, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_E33771651DE4D565.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748540864598108, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748540864598287, "dur": 1194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748540864599482, "dur": 910, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748540864600392, "dur": 415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748540864600807, "dur": 397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748540864601204, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748540864601585, "dur": 163330, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748540864595600, "dur": 1877, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748540864597488, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748540864597545, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": 1748540864597480, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_D60F4678B0C9138A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748540864597657, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748540864597800, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748540864597799, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_15AAF2E558C07C16.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748540864598098, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748540864598212, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748540864598269, "dur": 1321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748540864599590, "dur": 793, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748540864600431, "dur": 393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748540864600824, "dur": 384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748540864601208, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748540864601585, "dur": 163358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748540864595534, "dur": 1934, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748540864597484, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748540864597542, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1748540864597477, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_052C350FFD3D9DA1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748540864597807, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748540864597870, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748540864597868, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_7D7229C02E0DB889.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748540864598102, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748540864598295, "dur": 1146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748540864599441, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748540864599770, "dur": 625, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748540864600396, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748540864600821, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748540864601241, "dur": 348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748540864601590, "dur": 163343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748540864595782, "dur": 1715, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748540864597550, "dur": 195, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1748540864597500, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_E660793899F04DE4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748540864598036, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GIModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748540864598035, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_2DCA04472A508570.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748540864598112, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748540864598204, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748540864598298, "dur": 1253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748540864599551, "dur": 833, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748540864600384, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748540864600809, "dur": 428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748540864601237, "dur": 394, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748540864601631, "dur": 163305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748540864595856, "dur": 1663, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748540864597534, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748540864597594, "dur": 164, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1748540864597528, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_6C261F44AB06F2F8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748540864597799, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748540864597797, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_C9CFE1DD70D57119.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748540864597860, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748540864598107, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748540864598293, "dur": 1126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748540864599420, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748540864600071, "dur": 320, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748540864600391, "dur": 431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748540864600822, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748540864601226, "dur": 367, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748540864601593, "dur": 163308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748540864596120, "dur": 1463, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748540864597628, "dur": 169, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 8, "ts": 1748540864597585, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_5B115C4D92515B9E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748540864597798, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748540864598102, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748540864598101, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7574F99E59035FF8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748540864598252, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748540864598313, "dur": 1130, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748540864599490, "dur": 895, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748540864600385, "dur": 420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748540864600859, "dur": 356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748540864601216, "dur": 368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748540864601584, "dur": 163324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748540864596197, "dur": 1405, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748540864597604, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_B3BB0DA1997A2647.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748540864597658, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748540864597827, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748540864597924, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748540864598120, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748540864598274, "dur": 1209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748540864599484, "dur": 917, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748540864600402, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748540864600820, "dur": 385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748540864601206, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748540864601586, "dur": 163323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748540864596003, "dur": 1572, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748540864597634, "dur": 160, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 10, "ts": 1748540864597577, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_7CC3BC782BC99F3E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748540864598132, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748540864598267, "dur": 1122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748540864599390, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748540864600126, "dur": 298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748540864600424, "dur": 390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748540864600814, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748540864601206, "dur": 376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748540864601636, "dur": 163269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748540864596141, "dur": 1448, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748540864597598, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748540864597592, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_132925DC02CDA280.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748540864597657, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748540864597770, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748540864597769, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_431E85A617E2A342.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748540864597860, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748540864597931, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_0E8661F65C99947F.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748540864598103, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748540864598102, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_4F00803CEBBEFC1F.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748540864598271, "dur": 1335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748540864599606, "dur": 777, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748540864600421, "dur": 923, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748540864601344, "dur": 247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748540864601592, "dur": 163350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748540864596270, "dur": 1337, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748540864597609, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_318BC6ACF69CAB06.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748540864597667, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748540864597727, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_926D2B195BEC1C1F.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748540864597804, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748540864597867, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748540864597866, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_91015966EAE8A302.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748540864598124, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748540864598123, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_690369FE7ACF6B74.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748540864598307, "dur": 1060, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748540864599368, "dur": 777, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748540864600145, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748540864600393, "dur": 411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748540864600853, "dur": 378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748540864601231, "dur": 356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748540864601587, "dur": 163325, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748540864596164, "dur": 1432, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748540864597608, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748540864597598, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_4639AD57CB8F0BF2.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748540864597742, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748540864597801, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748540864597800, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_59ADA145A78A1AC4.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748540864597861, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748540864598126, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748540864598305, "dur": 985, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748540864599291, "dur": 810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748540864600102, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748540864600392, "dur": 423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748540864600816, "dur": 385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748540864601251, "dur": 342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748540864601593, "dur": 163353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748540864596364, "dur": 1264, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748540864597634, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1748540864597628, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_0789CA4FD66C3EE4.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748540864597731, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_169242F115DD75D8.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748540864598006, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1748540864598005, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_06BA14BCF0F515E9.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748540864598077, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748540864598279, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748540864598336, "dur": 1023, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748540864599359, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748540864600111, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748540864600389, "dur": 419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748540864600808, "dur": 395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748540864601203, "dur": 427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748540864601631, "dur": 163316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748540864596293, "dur": 1320, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748540864597623, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1748540864597616, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_8ED626F90EA59802.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1748540864597736, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1748540864597734, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_DC8E3421511E8CD6.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1748540864597798, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748540864598110, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 15, "ts": 1748540864598108, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_043AF99BA411347E.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1748540864598203, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748540864598266, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1748540864598584, "dur": 196, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Core.dll"}}, {"pid": 12345, "tid": 15, "ts": 1748540864599037, "dur": 174, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 15, "ts": 1748540864599246, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Web.dll"}}, {"pid": 12345, "tid": 15, "ts": 1748540864599873, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 15, "ts": 1748540864600027, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 15, "ts": 1748540864598382, "dur": 1920, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1748540864600302, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748540864600504, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1748540864600721, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748540864600902, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1748540864601117, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748540864601301, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1748540864601507, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748540864601687, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1748540864602329, "dur": 158698, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1748540864596317, "dur": 1302, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748540864597629, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1748540864597622, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_C2F00331D8C496E9.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1748540864597738, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1748540864597737, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_E3D37FEC8CA3287A.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1748540864597790, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748540864597923, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubstanceModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1748540864597921, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_674F48124BA3C8D6.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1748540864598129, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748540864598282, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748540864598335, "dur": 1047, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748540864599382, "dur": 806, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748540864600188, "dur": 198, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748540864600386, "dur": 466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748540864600852, "dur": 384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748540864601236, "dur": 364, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748540864601600, "dur": 163341, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748540864770750, "dur": 700, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 3528, "tid": 290, "ts": 1748540864771672, "dur": 1797, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 3528, "tid": 290, "ts": 1748540864773652, "dur": 8099, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 3528, "tid": 290, "ts": 1748540864771478, "dur": 10311, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}