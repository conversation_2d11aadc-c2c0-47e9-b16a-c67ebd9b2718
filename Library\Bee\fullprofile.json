{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 3528, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 3528, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 3528, "tid": 306, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 3528, "tid": 306, "ts": 1748542218879018, "dur": 7, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 3528, "tid": 306, "ts": 1748542218879038, "dur": 3, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 3528, "tid": 103079215104, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218694882, "dur": 14064, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218708947, "dur": 169625, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218708959, "dur": 19, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218708980, "dur": 18892, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218727881, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218727884, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218727906, "dur": 3, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218727910, "dur": 1070, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218728986, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218728988, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729055, "dur": 2, "ph": "X", "name": "ProcessMessages 2776", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729058, "dur": 62, "ph": "X", "name": "ReadAsync 2776", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729123, "dur": 1, "ph": "X", "name": "ProcessMessages 1761", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729127, "dur": 30, "ph": "X", "name": "ReadAsync 1761", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729160, "dur": 1, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729162, "dur": 40, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729205, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729242, "dur": 1, "ph": "X", "name": "ProcessMessages 1059", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729244, "dur": 33, "ph": "X", "name": "ReadAsync 1059", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729280, "dur": 23, "ph": "X", "name": "ReadAsync 1008", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729305, "dur": 24, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729333, "dur": 29, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729364, "dur": 1, "ph": "X", "name": "ProcessMessages 699", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729366, "dur": 32, "ph": "X", "name": "ReadAsync 699", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729400, "dur": 1, "ph": "X", "name": "ProcessMessages 832", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729402, "dur": 35, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729440, "dur": 1, "ph": "X", "name": "ProcessMessages 853", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729442, "dur": 20, "ph": "X", "name": "ReadAsync 853", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729464, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729495, "dur": 25, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729523, "dur": 22, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729546, "dur": 20, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729569, "dur": 22, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729593, "dur": 20, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729615, "dur": 22, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729639, "dur": 19, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729661, "dur": 19, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729682, "dur": 19, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729703, "dur": 17, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729723, "dur": 22, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729747, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729769, "dur": 19, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729791, "dur": 18, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729812, "dur": 19, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729834, "dur": 21, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729856, "dur": 16, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729875, "dur": 23, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729900, "dur": 20, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729922, "dur": 19, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729944, "dur": 20, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729965, "dur": 15, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218729983, "dur": 21, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730007, "dur": 17, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730026, "dur": 20, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730048, "dur": 18, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730069, "dur": 19, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730091, "dur": 18, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730111, "dur": 17, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730130, "dur": 64, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730195, "dur": 1, "ph": "X", "name": "ProcessMessages 2007", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730197, "dur": 17, "ph": "X", "name": "ReadAsync 2007", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730216, "dur": 22, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730245, "dur": 20, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730267, "dur": 20, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730289, "dur": 17, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730309, "dur": 19, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730330, "dur": 45, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730377, "dur": 1, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730379, "dur": 36, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730418, "dur": 1, "ph": "X", "name": "ProcessMessages 910", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730420, "dur": 33, "ph": "X", "name": "ReadAsync 910", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730455, "dur": 1, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730457, "dur": 27, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730486, "dur": 1, "ph": "X", "name": "ProcessMessages 763", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730488, "dur": 24, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730514, "dur": 20, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730537, "dur": 25, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730564, "dur": 16, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730583, "dur": 23, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730608, "dur": 21, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730632, "dur": 19, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730652, "dur": 22, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730678, "dur": 18, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730699, "dur": 19, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730724, "dur": 21, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730748, "dur": 21, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730771, "dur": 19, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730792, "dur": 17, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730812, "dur": 19, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730832, "dur": 19, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730853, "dur": 17, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730873, "dur": 20, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730895, "dur": 16, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730913, "dur": 51, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218730968, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218731038, "dur": 1, "ph": "X", "name": "ProcessMessages 203", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218731040, "dur": 175, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218731218, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218731238, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218731239, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218731292, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218731313, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218731386, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218731419, "dur": 25, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218731446, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218731448, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218731477, "dur": 72, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218731554, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218731585, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218731587, "dur": 27, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218731616, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218731619, "dur": 23, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218731645, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218731647, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218731683, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218731685, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218731713, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218731715, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218731739, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218731741, "dur": 24, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218731770, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218731796, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218731798, "dur": 23, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218731824, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218731856, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218731858, "dur": 24, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218731885, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218731887, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218731909, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218731929, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218731955, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218731995, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218732057, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218732059, "dur": 24, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218732087, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218732090, "dur": 25, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218732118, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218732120, "dur": 29, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218732152, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218732155, "dur": 24, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218732181, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218732183, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218732206, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218732207, "dur": 87, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218732298, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218732320, "dur": 597, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218732922, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218732945, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218732948, "dur": 569, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218733523, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218733550, "dur": 26, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218733577, "dur": 557, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218734151, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218734176, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218734198, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218734230, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218734286, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218734313, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218734314, "dur": 321, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218734639, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218734681, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218734702, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218734733, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218734766, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218734784, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218734786, "dur": 300, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218735090, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218735113, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218735133, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218735166, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218735182, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218735183, "dur": 292, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218735480, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218735507, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218735528, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218735557, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218735600, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218735617, "dur": 6579, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218742204, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218742207, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218742238, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218742241, "dur": 127040, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218869289, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218869293, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218869316, "dur": 637, "ph": "X", "name": "ProcessMessages 717", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218869955, "dur": 4926, "ph": "X", "name": "ReadAsync 717", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218874886, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218874890, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218874928, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 3528, "tid": 103079215104, "ts": 1748542218874931, "dur": 3638, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 3528, "tid": 306, "ts": 1748542218879043, "dur": 264, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 3528, "tid": 98784247808, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 3528, "tid": 98784247808, "ts": 1748542218694839, "dur": 7, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 3528, "tid": 98784247808, "ts": 1748542218694847, "dur": 14091, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 3528, "tid": 98784247808, "ts": 1748542218708938, "dur": 36, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 3528, "tid": 306, "ts": 1748542218879308, "dur": 5, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 3528, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 3528, "tid": 1, "ts": 1748542218500898, "dur": 860, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 3528, "tid": 1, "ts": 1748542218501761, "dur": 10489, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 3528, "tid": 1, "ts": 1748542218512251, "dur": 2913, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 3528, "tid": 306, "ts": 1748542218879314, "dur": 3, "ph": "X", "name": "", "args": {}}, {"pid": 3528, "tid": 94489280512, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218496927, "dur": 14077, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218511006, "dur": 8953, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218511012, "dur": 28, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218511044, "dur": 6, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218511051, "dur": 414, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218511471, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218511498, "dur": 1, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218511499, "dur": 63, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218511565, "dur": 1, "ph": "X", "name": "ProcessMessages 93", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218511567, "dur": 37, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218511607, "dur": 1, "ph": "X", "name": "ProcessMessages 1710", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218511610, "dur": 37, "ph": "X", "name": "ReadAsync 1710", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218511649, "dur": 28, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218511680, "dur": 1, "ph": "X", "name": "ProcessMessages 832", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218511682, "dur": 30, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218511715, "dur": 1, "ph": "X", "name": "ProcessMessages 936", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218511717, "dur": 35, "ph": "X", "name": "ReadAsync 936", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218511754, "dur": 45, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218511801, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218511810, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218511841, "dur": 1, "ph": "X", "name": "ProcessMessages 953", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218511843, "dur": 24, "ph": "X", "name": "ReadAsync 953", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218511868, "dur": 1, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218511870, "dur": 24, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218511897, "dur": 1, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218511899, "dur": 24, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218511925, "dur": 27, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218511955, "dur": 18, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218511975, "dur": 19, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218511997, "dur": 19, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512019, "dur": 22, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512044, "dur": 23, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512069, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512092, "dur": 19, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512114, "dur": 20, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512136, "dur": 34, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512173, "dur": 17, "ph": "X", "name": "ReadAsync 946", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512193, "dur": 19, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512214, "dur": 18, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512235, "dur": 18, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512255, "dur": 18, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512276, "dur": 22, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512300, "dur": 16, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512319, "dur": 21, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512342, "dur": 16, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512361, "dur": 24, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512387, "dur": 26, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512416, "dur": 23, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512441, "dur": 18, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512463, "dur": 22, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512487, "dur": 25, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512514, "dur": 1, "ph": "X", "name": "ProcessMessages 690", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512515, "dur": 19, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512536, "dur": 15, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512553, "dur": 17, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512572, "dur": 19, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512594, "dur": 18, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512614, "dur": 19, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512635, "dur": 18, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512656, "dur": 18, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512676, "dur": 19, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512697, "dur": 18, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512717, "dur": 20, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512739, "dur": 18, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512760, "dur": 18, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512780, "dur": 20, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512802, "dur": 19, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512823, "dur": 19, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512845, "dur": 18, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512866, "dur": 17, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512887, "dur": 19, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512908, "dur": 17, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512928, "dur": 18, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512948, "dur": 15, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512965, "dur": 15, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218512983, "dur": 19, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218513005, "dur": 23, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218513029, "dur": 19, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218513050, "dur": 16, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218513068, "dur": 18, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218513089, "dur": 17, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218513108, "dur": 18, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218513129, "dur": 20, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218513151, "dur": 22, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218513175, "dur": 18, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218513196, "dur": 15, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218513213, "dur": 16, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218513232, "dur": 25, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218513260, "dur": 24, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218513286, "dur": 16, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218513304, "dur": 18, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218513324, "dur": 17, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218513344, "dur": 19, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218513365, "dur": 21, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218513388, "dur": 19, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218513410, "dur": 22, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218513434, "dur": 31, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218513468, "dur": 19, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218513490, "dur": 28, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218513520, "dur": 18, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218513540, "dur": 24, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218513566, "dur": 14, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218513583, "dur": 21, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218513606, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218513624, "dur": 206, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218513835, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218513865, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218513867, "dur": 28, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218513899, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218513901, "dur": 28, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218513932, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218513934, "dur": 27, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218513963, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218513966, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218514010, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218514013, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218514043, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218514046, "dur": 23, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218514072, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218514074, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218514102, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218514104, "dur": 23, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218514130, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218514132, "dur": 21, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218514155, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218514157, "dur": 21, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218514180, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218514182, "dur": 18, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218514203, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218514225, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218514249, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218514273, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218514274, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218514298, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218514300, "dur": 19, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218514322, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218514343, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218514344, "dur": 89, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218514439, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218514474, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218514476, "dur": 26, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218514505, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218514508, "dur": 24, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218514535, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218514537, "dur": 22, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218514561, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218514563, "dur": 20, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218514586, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218514588, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218514616, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218514618, "dur": 191, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218514814, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218514841, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218514843, "dur": 1916, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218516763, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218516766, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218516799, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218516865, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218516888, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 3528, "tid": 94489280512, "ts": 1748542218516890, "dur": 3063, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 3528, "tid": 306, "ts": 1748542218879318, "dur": 200, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 3528, "tid": 90194313216, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 3528, "tid": 90194313216, "ts": 1748542218496895, "dur": 18278, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 3528, "tid": 90194313216, "ts": 1748542218515174, "dur": 32, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 3528, "tid": 306, "ts": 1748542218879520, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 3528, "tid": 85899345920, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 3528, "tid": 85899345920, "ts": 1748542218494523, "dur": 25462, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 3528, "tid": 85899345920, "ts": 1748542218494658, "dur": 1620, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 3528, "tid": 85899345920, "ts": 1748542218519990, "dur": 173133, "ph": "X", "name": "await ExecuteBuildProgram", "args": {}}, {"pid": 3528, "tid": 85899345920, "ts": 1748542218693137, "dur": 185465, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 3528, "tid": 85899345920, "ts": 1748542218693220, "dur": 1579, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 3528, "tid": 85899345920, "ts": 1748542218878605, "dur": 38, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 3528, "tid": 85899345920, "ts": 1748542218878616, "dur": 13, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 3528, "tid": 306, "ts": 1748542218879525, "dur": 17, "ph": "X", "name": "BuildAsync", "args": {}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "netcorerun.dll"}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 35942, "tid": 1, "ts": 1748542218572980, "dur": 106836, "ph": "X", "name": "BuildProgram", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1748542218573829, "dur": 32267, "ph": "X", "name": "BuildProgramContextConstructor", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1748542218660450, "dur": 3771, "ph": "X", "name": "OutputData.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1748542218664225, "dur": 15581, "ph": "X", "name": "Backend.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1748542218665457, "dur": 12203, "ph": "X", "name": "JsonToString", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1748542218685885, "dur": 1077, "ph": "X", "name": "", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1748542218685341, "dur": 1836, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748542218510394, "dur": 971, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748542218511373, "dur": 71, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748542218511476, "dur": 672, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748542218512169, "dur": 2167, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748542218514338, "dur": 2862, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748542218517201, "dur": 258, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748542218517584, "dur": 840, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748542218512049, "dur": 2306, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542218514367, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748542218514429, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1748542218514357, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_FB0CA54B74E90E14.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748542218514515, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542218514583, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542218514932, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542218515031, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542218515133, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542218515258, "dur": 1105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542218516364, "dur": 786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542218517150, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542218512002, "dur": 2348, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542218514364, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748542218514428, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1748542218514353, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_D60F4678B0C9138A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748542218514590, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748542218514589, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_169242F115DD75D8.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748542218514735, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748542218514734, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_91015966EAE8A302.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748542218515034, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748542218515033, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_690369FE7ACF6B74.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748542218515140, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542218515260, "dur": 864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542218516124, "dur": 924, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542218517048, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542218511997, "dur": 2346, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542218514362, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748542218514424, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1748542218514351, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_052C350FFD3D9DA1.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748542218514482, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542218514567, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542218514687, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_092BE7AA20DD0577.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748542218514947, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_2DCA04472A508570.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748542218515080, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542218515269, "dur": 789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542218516058, "dur": 1122, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542218512313, "dur": 2162, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542218514486, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748542218514477, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_132925DC02CDA280.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748542218514538, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542218514615, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748542218514614, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_95E784EC8E0862CB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748542218515048, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542218515232, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542218515297, "dur": 1132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542218516456, "dur": 743, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542218512153, "dur": 2252, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542218514414, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748542218514408, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_E660793899F04DE4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748542218514482, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542218514591, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542218514682, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_E2964BE09A8A93C0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748542218514736, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542218514905, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542218515006, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748542218515005, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_470B0DA014C860E0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748542218515069, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542218515263, "dur": 867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542218516130, "dur": 983, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542218517114, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542218512180, "dur": 2231, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542218514471, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1748542218514415, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_6C261F44AB06F2F8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748542218514578, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542218514639, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748542218514637, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_C9CFE1DD70D57119.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748542218514801, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542218514883, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748542218514881, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_E02FF5D4853CF3B3.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748542218515031, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542218515175, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542218515259, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542218515315, "dur": 984, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542218516299, "dur": 852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542218517152, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542218512213, "dur": 2241, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542218514458, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_2774BB72C3E9800F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748542218514569, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748542218514568, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_A67C459F9DC5AEAD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748542218514622, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542218514738, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748542218514736, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_7D7229C02E0DB889.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748542218514796, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542218514856, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_52952EA4AE156341.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748542218514966, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GameCenterModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748542218514958, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_2559EF663B29FF05.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748542218515038, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542218515282, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748542218515781, "dur": 181, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Core.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748542218516216, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748542218516427, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748542218516529, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748542218516807, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748542218516860, "dur": 147, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748542218515527, "dur": 1842, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748542218517370, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542218512248, "dur": 2213, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542218514596, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542218514681, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748542218514680, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_59ADA145A78A1AC4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748542218514789, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542218514877, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748542218514876, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_E1B14D67211F502C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748542218515013, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748542218515012, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_4F00803CEBBEFC1F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748542218515146, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542218515242, "dur": 1173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542218516416, "dur": 114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542218516530, "dur": 667, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748542218512289, "dur": 2179, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748542218514484, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748542218514471, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_5B115C4D92515B9E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748542218514593, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748542218514592, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_DC8E3421511E8CD6.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748542218514901, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_1882500C00DD15DD.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748542218515038, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748542218515151, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748542218515248, "dur": 1080, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748542218516328, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748542218517165, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748542218512335, "dur": 2149, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748542218514492, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748542218514486, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_4639AD57CB8F0BF2.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748542218514545, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748542218514686, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityTestProtocolModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748542218514685, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_E33771651DE4D565.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748542218514746, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748542218515059, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748542218515283, "dur": 1259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748542218516542, "dur": 656, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748542218512072, "dur": 2327, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748542218514457, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 11, "ts": 1748542218514402, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_958DBB398EA19D56.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748542218514554, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748542218514627, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748542218514626, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_0954F19AD29BDF65.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748542218515035, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748542218515241, "dur": 1092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748542218516334, "dur": 822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748542218512375, "dur": 2115, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748542218514492, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_B3BB0DA1997A2647.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748542218515044, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748542218515186, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748542218515287, "dur": 1448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748542218516736, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748542218512391, "dur": 2104, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748542218514498, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_318BC6ACF69CAB06.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748542218514596, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748542218514595, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_E3D37FEC8CA3287A.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748542218514727, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748542218514785, "dur": 179, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748542218514784, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_D970D7A0A54F1A69.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748542218514967, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748542218515067, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748542218515256, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748542218516297, "dur": 950, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748542218512421, "dur": 2082, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748542218514516, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1748542218514506, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_8ED626F90EA59802.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748542218514575, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748542218514676, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748542218514895, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_06BA14BCF0F515E9.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748542218515030, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748542218515237, "dur": 1264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748542218516501, "dur": 702, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748542218512443, "dur": 2066, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748542218514522, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1748542218514512, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_C2F00331D8C496E9.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1748542218514621, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1748542218514619, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_94F18CE7015FE34E.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1748542218514687, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748542218514849, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1748542218514848, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_FC0A74906F4439E8.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1748542218514936, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748542218515012, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1748542218515011, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7574F99E59035FF8.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1748542218515145, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748542218515265, "dur": 1039, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748542218516305, "dur": 846, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748542218517151, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748542218512513, "dur": 2002, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748542218514516, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_0789CA4FD66C3EE4.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1748542218514617, "dur": 118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1748542218514616, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_431E85A617E2A342.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1748542218514738, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748542218514825, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1748542218514823, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_0E8661F65C99947F.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1748542218514894, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748542218515079, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748542218515245, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1748542218515314, "dur": 1077, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748542218516391, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748542218516880, "dur": 332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748542218519695, "dur": 690, "ph": "X", "name": "ProfilerWriteOutput"}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748542218709384, "dur": 19510, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748542218728969, "dur": 363, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748542218729349, "dur": 2065, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748542218731416, "dur": 142660, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748542218874077, "dur": 336, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748542218875338, "dur": 1165, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748542218729401, "dur": 2020, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542218731423, "dur": 1600, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542218733023, "dur": 1243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542218734266, "dur": 364, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542218734630, "dur": 516, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542218735146, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542218735557, "dur": 407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748542218735965, "dur": 138117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542218729528, "dur": 1907, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542218731444, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748542218731519, "dur": 251, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1748542218731437, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_D60F4678B0C9138A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748542218731933, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542218732046, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_59ADA145A78A1AC4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748542218732099, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542218732174, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748542218732173, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_8AA433C90050CAC8.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748542218732238, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542218732358, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542218732633, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542218733438, "dur": 930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542218734368, "dur": 261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542218734629, "dur": 516, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542218735145, "dur": 412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542218735557, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748542218735971, "dur": 138125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542218730006, "dur": 1560, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542218731618, "dur": 430, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1748542218731567, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_C2F00331D8C496E9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748542218732299, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542218732354, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748542218732353, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_9819ED8ACBA729A3.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748542218732457, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542218732653, "dur": 821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542218733475, "dur": 822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542218734297, "dur": 316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542218734614, "dur": 501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542218735116, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542218735531, "dur": 441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748542218735972, "dur": 138119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542218729610, "dur": 1830, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542218731452, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748542218731520, "dur": 319, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": 1748542218731443, "dur": 397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_FB0CA54B74E90E14.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748542218731927, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542218731990, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_94F18CE7015FE34E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748542218732080, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_91015966EAE8A302.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748542218732178, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748542218732177, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_0E8661F65C99947F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748542218732233, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542218732293, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748542218732292, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_040BEB3B4DCBE197.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748542218732348, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542218732529, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542218733526, "dur": 472, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748542218733999, "dur": 641, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542218734640, "dur": 495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542218735135, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542218735524, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748542218735983, "dur": 138106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542218729635, "dur": 1855, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542218731501, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748542218731569, "dur": 283, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1748542218731494, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_958DBB398EA19D56.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748542218731933, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542218732242, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542218732294, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_5758047E8BFC2F0C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748542218732378, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748542218732377, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_E022B0A53AAB81FC.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748542218732477, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542218732590, "dur": 899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542218733489, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542218734025, "dur": 591, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542218734628, "dur": 487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542218735115, "dur": 411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542218735526, "dur": 444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748542218735970, "dur": 138127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542218729667, "dur": 1831, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542218731557, "dur": 291, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1748542218731501, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_E660793899F04DE4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748542218731901, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748542218731899, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_DC8E3421511E8CD6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748542218731970, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542218732353, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542218732494, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542218732601, "dur": 1267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542218733868, "dur": 767, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542218734636, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542218735117, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542218735173, "dur": 370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542218735543, "dur": 434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748542218735977, "dur": 138109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542218729696, "dur": 1808, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542218731513, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748542218731577, "dur": 341, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1748542218731506, "dur": 413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_6C261F44AB06F2F8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748542218731919, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542218732103, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542218732197, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ProfilerModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748542218732196, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_C3D83151F1CB96D7.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748542218732258, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542218732394, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542218732491, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542218732603, "dur": 840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542218733443, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542218734336, "dur": 296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542218734633, "dur": 500, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542218735133, "dur": 384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542218735520, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542218735580, "dur": 385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748542218735966, "dur": 138094, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542218729726, "dur": 1784, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542218731521, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748542218731587, "dur": 382, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 8, "ts": 1748542218731512, "dur": 458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_2774BB72C3E9800F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748542218731970, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542218732070, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748542218732068, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_FFCF669E316445AF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748542218732124, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542218732181, "dur": 130, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ScreenCaptureModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748542218732180, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_605FAFF9AD97DAD0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748542218732329, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542218732463, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748542218732462, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_043AF99BA411347E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748542218732605, "dur": 835, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542218733441, "dur": 850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542218734291, "dur": 338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542218734629, "dur": 492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542218735121, "dur": 430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542218735551, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748542218735994, "dur": 138084, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748542218729745, "dur": 1770, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748542218731524, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748542218731579, "dur": 344, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": 1748542218731517, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_7CC3BC782BC99F3E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748542218731924, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748542218732242, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748542218732300, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748542218732299, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_43B074E6D83CA464.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748542218732352, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748542218732465, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748542218732464, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_690369FE7ACF6B74.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748542218732647, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748542218733542, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748542218733688, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Buffers.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748542218733819, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748542218733873, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748542218733925, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748542218734041, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748542218732760, "dur": 1768, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1748542218734529, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748542218734659, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748542218734746, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1748542218735031, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748542218735154, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748542218735231, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1748542218735438, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748542218735633, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1748542218735858, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748542218735986, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748542218736237, "dur": 6327, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1748542218736066, "dur": 6499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1748542218742570, "dur": 54, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748542218742635, "dur": 127063, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1748542218729770, "dur": 1752, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748542218731584, "dur": 387, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 10, "ts": 1748542218731526, "dur": 447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_5B115C4D92515B9E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748542218731973, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748542218732175, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748542218732241, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.JSONSerializeModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748542218732239, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_46780274992BAE6E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748542218732369, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748542218732530, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748542218732635, "dur": 826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748542218733461, "dur": 918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748542218734379, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748542218734639, "dur": 479, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748542218735118, "dur": 431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748542218735549, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748542218736006, "dur": 138074, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748542218729787, "dur": 1742, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748542218731586, "dur": 381, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 11, "ts": 1748542218731531, "dur": 437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_132925DC02CDA280.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748542218731968, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748542218732235, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_E02FF5D4853CF3B3.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748542218732360, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748542218732588, "dur": 797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748542218733385, "dur": 830, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748542218734216, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748542218734641, "dur": 510, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748542218735151, "dur": 397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748542218735549, "dur": 427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748542218735977, "dur": 138110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748542218729818, "dur": 1719, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748542218731547, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748542218731600, "dur": 365, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 12, "ts": 1748542218731540, "dur": 426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_4639AD57CB8F0BF2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748542218732180, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748542218732179, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_881D2A10C4F9DCE7.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748542218732233, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748542218732362, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748542218732361, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_EBC2945A5C8EC5BE.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748542218732592, "dur": 787, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748542218733379, "dur": 831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748542218734211, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748542218734661, "dur": 456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748542218735117, "dur": 402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748542218735572, "dur": 388, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748542218735960, "dur": 138103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748542218729843, "dur": 1701, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748542218731599, "dur": 365, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 13, "ts": 1748542218731547, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_B3BB0DA1997A2647.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748542218731966, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748542218732116, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748542218732114, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_9188A12F15CB614B.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748542218732359, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748542218732478, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748542218732628, "dur": 796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748542218733424, "dur": 938, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748542218734363, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748542218734615, "dur": 501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748542218735117, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748542218735525, "dur": 428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748542218735953, "dur": 138124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748542218729936, "dur": 1624, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748542218731615, "dur": 424, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 14, "ts": 1748542218731563, "dur": 477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_8ED626F90EA59802.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748542218732083, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1748542218732082, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_7D7229C02E0DB889.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748542218732194, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_52952EA4AE156341.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748542218732245, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748542218732301, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_2DCA04472A508570.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748542218732397, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1748542218732396, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7574F99E59035FF8.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748542218732491, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748542218732618, "dur": 876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748542218733523, "dur": 1107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748542218734630, "dur": 483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748542218735116, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748542218735182, "dur": 376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748542218735558, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748542218735968, "dur": 138124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748542218729872, "dur": 1678, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748542218731605, "dur": 432, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 15, "ts": 1748542218731552, "dur": 486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_318BC6ACF69CAB06.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1748542218732241, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_46987855B0E3A6A0.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1748542218732375, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748542218732490, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748542218732579, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748542218732644, "dur": 978, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748542218733622, "dur": 990, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748542218734679, "dur": 442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748542218735133, "dur": 399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748542218735533, "dur": 419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748542218735952, "dur": 138132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748542218729507, "dur": 1919, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748542218731442, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1748542218731515, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 16, "ts": 1748542218731434, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_052C350FFD3D9DA1.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1748542218731857, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748542218731913, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1748542218731912, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_95E784EC8E0862CB.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1748542218731973, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748542218732072, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1748542218732070, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_7CF00AAB3B07EE8A.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1748542218732171, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748542218732237, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1748542218732236, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_2A0F5EACF40AB8B7.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1748542218732297, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748542218732371, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1748542218732370, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_470B0DA014C860E0.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1748542218732488, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748542218732593, "dur": 796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748542218733389, "dur": 1013, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748542218734403, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748542218734641, "dur": 503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748542218735144, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748542218735522, "dur": 453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748542218735975, "dur": 138117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748542218877827, "dur": 904, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 3528, "tid": 306, "ts": 1748542218879588, "dur": 30, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"}}, {"pid": 3528, "tid": 306, "ts": 1748542218879831, "dur": 22, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 3528, "tid": 306, "ts": 1748542218880210, "dur": 821, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend2.traceevents"}}, {"pid": 3528, "tid": 306, "ts": 1748542218879684, "dur": 147, "ph": "X", "name": "buildprogram0.traceevents", "args": {}}, {"pid": 3528, "tid": 306, "ts": 1748542218879898, "dur": 311, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 3528, "tid": 306, "ts": 1748542218881092, "dur": 8886, "ph": "X", "name": "backend2.traceevents", "args": {}}, {"pid": 3528, "tid": 306, "ts": 1748542218879028, "dur": 11017, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}