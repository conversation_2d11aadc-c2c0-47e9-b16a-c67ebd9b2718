{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 3528, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 3528, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 3528, "tid": 278, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 3528, "tid": 278, "ts": 1748540693592159, "dur": 27, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 3528, "tid": 278, "ts": 1748540693592201, "dur": 6, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 3528, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 3528, "tid": 1, "ts": 1748540692683206, "dur": 4351, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 3528, "tid": 1, "ts": 1748540692687563, "dur": 10328, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 3528, "tid": 1, "ts": 1748540692697893, "dur": 2274, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 3528, "tid": 278, "ts": 1748540693592209, "dur": 11, "ph": "X", "name": "", "args": {}}, {"pid": 3528, "tid": 47244640256, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692683172, "dur": 14172, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692697345, "dur": 894051, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692697353, "dur": 44, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692697399, "dur": 4, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692697405, "dur": 466, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692697874, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692697876, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692697901, "dur": 1, "ph": "X", "name": "ProcessMessages 736", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692697903, "dur": 22, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692697929, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692697931, "dur": 33, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692697967, "dur": 27, "ph": "X", "name": "ReadAsync 988", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692697997, "dur": 1, "ph": "X", "name": "ProcessMessages 692", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692697999, "dur": 31, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698033, "dur": 27, "ph": "X", "name": "ReadAsync 1024", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698063, "dur": 1, "ph": "X", "name": "ProcessMessages 758", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698065, "dur": 31, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698099, "dur": 1, "ph": "X", "name": "ProcessMessages 687", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698101, "dur": 46, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698151, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698181, "dur": 1, "ph": "X", "name": "ProcessMessages 1059", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698183, "dur": 86, "ph": "X", "name": "ReadAsync 1059", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698272, "dur": 1, "ph": "X", "name": "ProcessMessages 2007", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698274, "dur": 29, "ph": "X", "name": "ReadAsync 2007", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698306, "dur": 26, "ph": "X", "name": "ReadAsync 938", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698335, "dur": 1, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698336, "dur": 28, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698367, "dur": 1, "ph": "X", "name": "ProcessMessages 806", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698369, "dur": 21, "ph": "X", "name": "ReadAsync 806", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698393, "dur": 16, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698411, "dur": 21, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698434, "dur": 20, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698457, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698459, "dur": 20, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698481, "dur": 21, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698504, "dur": 1, "ph": "X", "name": "ProcessMessages 690", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698506, "dur": 30, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698538, "dur": 25, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698566, "dur": 19, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698588, "dur": 23, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698613, "dur": 19, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698634, "dur": 20, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698657, "dur": 16, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698676, "dur": 21, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698699, "dur": 19, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698721, "dur": 20, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698744, "dur": 20, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698766, "dur": 43, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698811, "dur": 1, "ph": "X", "name": "ProcessMessages 1075", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698812, "dur": 19, "ph": "X", "name": "ReadAsync 1075", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698833, "dur": 22, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698858, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698859, "dur": 22, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698884, "dur": 20, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698908, "dur": 15, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698925, "dur": 18, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698945, "dur": 21, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698969, "dur": 23, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692698994, "dur": 20, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699016, "dur": 20, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699039, "dur": 20, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699061, "dur": 18, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699081, "dur": 19, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699102, "dur": 20, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699125, "dur": 20, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699147, "dur": 19, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699168, "dur": 16, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699187, "dur": 20, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699209, "dur": 22, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699233, "dur": 19, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699255, "dur": 19, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699277, "dur": 19, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699298, "dur": 14, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699314, "dur": 17, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699334, "dur": 16, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699352, "dur": 23, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699377, "dur": 19, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699400, "dur": 20, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699422, "dur": 14, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699440, "dur": 21, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699464, "dur": 16, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699483, "dur": 47, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699532, "dur": 1, "ph": "X", "name": "ProcessMessages 1107", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699533, "dur": 20, "ph": "X", "name": "ReadAsync 1107", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699555, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699558, "dur": 25, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699586, "dur": 15, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699611, "dur": 22, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699635, "dur": 23, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699662, "dur": 20, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699684, "dur": 17, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699703, "dur": 18, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699723, "dur": 20, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699746, "dur": 19, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699767, "dur": 21, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699791, "dur": 18, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699812, "dur": 19, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699834, "dur": 19, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699855, "dur": 24, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699881, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699882, "dur": 23, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699907, "dur": 18, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699928, "dur": 15, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699945, "dur": 28, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699976, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692699997, "dur": 188, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700190, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700220, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700221, "dur": 23, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700248, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700250, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700278, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700304, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700307, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700334, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700363, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700391, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700394, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700423, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700427, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700457, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700460, "dur": 22, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700484, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700486, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700522, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700525, "dur": 21, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700549, "dur": 28, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700580, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700605, "dur": 7, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700612, "dur": 19, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700634, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700635, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700657, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700659, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700684, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700704, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700706, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700727, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700749, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700751, "dur": 19, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700774, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700836, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700862, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700864, "dur": 24, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700892, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700894, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700919, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700921, "dur": 22, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700946, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700948, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700972, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700974, "dur": 20, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700996, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692700998, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692701020, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692701022, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692701052, "dur": 2087, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692703143, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692703146, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692703165, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692703186, "dur": 84, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692703274, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692703307, "dur": 253, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692703566, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692703592, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692703613, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692703636, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692703639, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692703694, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692703713, "dur": 254, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692703971, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692703998, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692704020, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692704044, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692704045, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692704069, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692704094, "dur": 251, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692704350, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692704370, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692704409, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692704431, "dur": 61, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692704495, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692704515, "dur": 527, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692705048, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692705076, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540692705080, "dur": 877178, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540693582266, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540693582270, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540693582299, "dur": 1133, "ph": "X", "name": "ProcessMessages 4781", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540693583434, "dur": 4711, "ph": "X", "name": "ReadAsync 4781", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540693588150, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540693588153, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540693588180, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 3528, "tid": 47244640256, "ts": 1748540693588182, "dur": 3206, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 3528, "tid": 278, "ts": 1748540693592222, "dur": 428, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 3528, "tid": 42949672960, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 3528, "tid": 42949672960, "ts": 1748540692683136, "dur": 17040, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 3528, "tid": 42949672960, "ts": 1748540692700177, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 3528, "tid": 42949672960, "ts": 1748540692700178, "dur": 25, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 3528, "tid": 278, "ts": 1748540693592653, "dur": 15, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 3528, "tid": 38654705664, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 3528, "tid": 38654705664, "ts": 1748540692680544, "dur": 910901, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 3528, "tid": 38654705664, "ts": 1748540692680656, "dur": 2117, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 3528, "tid": 38654705664, "ts": 1748540693591449, "dur": 61, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 3528, "tid": 38654705664, "ts": 1748540693591468, "dur": 23, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 3528, "tid": 38654705664, "ts": 1748540693591513, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 3528, "tid": 278, "ts": 1748540693592670, "dur": 13, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748540692697021, "dur": 1017, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748540692698050, "dur": 72, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748540692698156, "dur": 362, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748540692698532, "dur": 2134, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748540692700667, "dur": 886810, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748540693587477, "dur": 406, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748540693587918, "dur": 50, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748540693588821, "dur": 980, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748540692698597, "dur": 2075, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748540692700690, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748540692700745, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1748540692700681, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_052C350FFD3D9DA1.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748540692700963, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748540692700961, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_95E784EC8E0862CB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748540692701048, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748540692701414, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748540692701524, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748540692701626, "dur": 903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748540692702529, "dur": 878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748540692703407, "dur": 441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748540692703848, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748540692704266, "dur": 407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748540692704673, "dur": 368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748540692705087, "dur": 882409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748540692698634, "dur": 2052, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748540692700701, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748540692700763, "dur": 178, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1748540692700688, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_FB0CA54B74E90E14.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748540692700942, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748540692701041, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748540692701039, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_A4331CFC950ED6D5.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748540692701150, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748540692701149, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_D970D7A0A54F1A69.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748540692701426, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748540692701425, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_690369FE7ACF6B74.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748540692701528, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748540692701632, "dur": 980, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748540692702613, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748540692703378, "dur": 439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748540692703863, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748540692704264, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748540692704675, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748540692705054, "dur": 882428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748540692698628, "dur": 2053, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748540692700696, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748540692700747, "dur": 178, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1748540692700683, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_D60F4678B0C9138A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748540692700971, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748540692700970, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_431E85A617E2A342.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748540692701421, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748540692701420, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_4F00803CEBBEFC1F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748540692701614, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748540692702478, "dur": 1033, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748540692703511, "dur": 327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748540692703838, "dur": 424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748540692704262, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748540692704666, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748540692705048, "dur": 882432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748540692698658, "dur": 2035, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748540692700705, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748540692700761, "dur": 220, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": 1748540692700695, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_958DBB398EA19D56.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748540692701188, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubstanceModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748540692701186, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_674F48124BA3C8D6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748540692701340, "dur": 187, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GIModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748540692701339, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_2DCA04472A508570.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748540692701628, "dur": 894, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748540692702522, "dur": 965, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748540692703487, "dur": 345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748540692703833, "dur": 427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748540692704261, "dur": 411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748540692704672, "dur": 387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748540692705059, "dur": 882425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748540692698684, "dur": 2017, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748540692700711, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748540692700766, "dur": 277, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1748540692700704, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_E660793899F04DE4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748540692701178, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748540692701176, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_89B4F204A66FCD3B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748540692701259, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748540692701424, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748540692701422, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_043AF99BA411347E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748540692701601, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748540692701662, "dur": 1110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748540692702772, "dur": 1051, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748540692703823, "dur": 433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748540692704256, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748540692704671, "dur": 374, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748540692705045, "dur": 882426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748540692698715, "dur": 1993, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748540692700771, "dur": 181, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1748540692700711, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_6C261F44AB06F2F8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748540692700953, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748540692701105, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_7CF00AAB3B07EE8A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748540692701423, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748540692701525, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748540692701586, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748540692701654, "dur": 952, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748540692702606, "dur": 860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748540692703467, "dur": 376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748540692703843, "dur": 412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748540692704300, "dur": 374, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748540692704674, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748540692705056, "dur": 882430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748540692698740, "dur": 1976, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748540692700776, "dur": 190, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1748540692700719, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_2774BB72C3E9800F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748540692700967, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748540692701041, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_C9CFE1DD70D57119.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748540692701271, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748540692701411, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748540692701410, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_470B0DA014C860E0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748540692701630, "dur": 861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748540692702491, "dur": 1100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748540692703591, "dur": 231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748540692703822, "dur": 443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748540692704265, "dur": 415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748540692704680, "dur": 369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748540692705050, "dur": 882440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748540692698766, "dur": 1958, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748540692700735, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748540692700793, "dur": 246, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 8, "ts": 1748540692700726, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_7CC3BC782BC99F3E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748540692701040, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748540692701271, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_22B5366951C60B1F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748540692701441, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748540692701535, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748540692701680, "dur": 953, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748540692702662, "dur": 333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748540692702995, "dur": 862, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748540692703857, "dur": 396, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748540692704297, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748540692704670, "dur": 372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748540692705042, "dur": 882434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748540692698790, "dur": 1939, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748540692700741, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748540692700792, "dur": 207, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": 1748540692700732, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_5B115C4D92515B9E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748540692701002, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748540692701117, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748540692701116, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_91015966EAE8A302.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748540692701276, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748540692701275, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_E1B14D67211F502C.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748540692701389, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_0E8B52DF278607E5.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748540692701440, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748540692701609, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748540692701666, "dur": 843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748540692702510, "dur": 941, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748540692703451, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748540692703852, "dur": 407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748540692704260, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748540692704668, "dur": 376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748540692705044, "dur": 882429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748540692698819, "dur": 1918, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748540692700749, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748540692700804, "dur": 245, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 10, "ts": 1748540692700739, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_132925DC02CDA280.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748540692701051, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748540692701216, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ScreenCaptureModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748540692701215, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_605FAFF9AD97DAD0.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748540692701417, "dur": 145, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748540692701416, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_E022B0A53AAB81FC.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748540692701601, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748540692702072, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748540692702266, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748540692702521, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748540692702580, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748540692702813, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748540692702963, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748540692703135, "dur": 198, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748540692701716, "dur": 1998, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1748540692703716, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748540692703860, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748540692703948, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1748540692704180, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748540692704289, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748540692704371, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1748540692704581, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748540692704761, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1748540692704965, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748540692705174, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1748540692705808, "dur": 877183, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1748540692698849, "dur": 1896, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748540692700759, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748540692700749, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_4639AD57CB8F0BF2.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748540692700976, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748540692701048, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748540692701046, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_15AAF2E558C07C16.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748540692701177, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748540692701443, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748540692701550, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748540692701691, "dur": 948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748540692702663, "dur": 1157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748540692703820, "dur": 441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748540692704262, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748540692704720, "dur": 323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748540692705043, "dur": 882431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748540692698877, "dur": 1881, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748540692700771, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748540692700761, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_B3BB0DA1997A2647.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748540692700834, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748540692700953, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748540692700952, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_169242F115DD75D8.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748540692701115, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748540692701417, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748540692701523, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748540692701634, "dur": 926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748540692702573, "dur": 1050, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748540692703623, "dur": 391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748540692704014, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748540692704263, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748540692704672, "dur": 381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748540692705053, "dur": 882435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748540692698896, "dur": 1879, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748540692700777, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_318BC6ACF69CAB06.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748540692700835, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748540692700890, "dur": 2102, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748540692702992, "dur": 826, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748540692703861, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748540692704264, "dur": 409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748540692704674, "dur": 366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748540692705085, "dur": 882406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748540692698919, "dur": 1861, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748540692700793, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1748540692700782, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_8ED626F90EA59802.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748540692700985, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1748540692700984, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_94F18CE7015FE34E.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748540692701047, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748540692701213, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_881D2A10C4F9DCE7.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748540692701431, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748540692701626, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748540692701688, "dur": 933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748540692702621, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748540692702976, "dur": 845, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748540692703821, "dur": 444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748540692704265, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748540692704669, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748540692705047, "dur": 882436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748540692699006, "dur": 1788, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748540692700805, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1748540692700795, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_0789CA4FD66C3EE4.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1748540692700956, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1748540692700955, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_DC8E3421511E8CD6.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1748540692701169, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748540692701298, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1748540692701296, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_46987855B0E3A6A0.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1748540692701383, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748540692701535, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748540692701616, "dur": 1177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748540692702793, "dur": 1031, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748540692703825, "dur": 433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748540692704259, "dur": 405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748540692704711, "dur": 335, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748540692705046, "dur": 882432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748540692698935, "dur": 1853, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748540692700801, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1748540692700790, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_C2F00331D8C496E9.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1748540692700960, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1748540692700959, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_E3D37FEC8CA3287A.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1748540692701419, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1748540692701418, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7574F99E59035FF8.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1748540692701636, "dur": 934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748540692702573, "dur": 952, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748540692703525, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748540692703828, "dur": 429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748540692704258, "dur": 409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748540692704667, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748540692705047, "dur": 882422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748540693590922, "dur": 807, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 3528, "tid": 278, "ts": 1748540693592746, "dur": 1569, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 3528, "tid": 278, "ts": 1748540693594406, "dur": 9515, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 3528, "tid": 278, "ts": 1748540693592188, "dur": 11768, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}