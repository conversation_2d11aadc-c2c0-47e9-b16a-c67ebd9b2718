{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 3528, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 3528, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 3528, "tid": 25, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 3528, "tid": 25, "ts": 1748537729868874, "dur": 467, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 3528, "tid": 25, "ts": 1748537729872176, "dur": 671, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 3528, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 3528, "tid": 1, "ts": 1748537729483282, "dur": 22466, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 3528, "tid": 1, "ts": 1748537729505754, "dur": 31866, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 3528, "tid": 1, "ts": 1748537729537626, "dur": 98451, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 3528, "tid": 25, "ts": 1748537729872850, "dur": 14, "ph": "X", "name": "", "args": {}}, {"pid": 3528, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729481510, "dur": 5416, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729486927, "dur": 373125, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729487742, "dur": 2246, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729489999, "dur": 714, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729490726, "dur": 5424, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729496158, "dur": 217, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729496379, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729496433, "dur": 769, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729497206, "dur": 11591, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729508805, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729508807, "dur": 173, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729508984, "dur": 580, "ph": "X", "name": "ProcessMessages 3470", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729509567, "dur": 333, "ph": "X", "name": "ReadAsync 3470", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729509903, "dur": 7, "ph": "X", "name": "ProcessMessages 16404", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729509911, "dur": 107, "ph": "X", "name": "ReadAsync 16404", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729510020, "dur": 2, "ph": "X", "name": "ProcessMessages 3109", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729510023, "dur": 114, "ph": "X", "name": "ReadAsync 3109", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729510140, "dur": 2, "ph": "X", "name": "ProcessMessages 3121", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729510143, "dur": 83, "ph": "X", "name": "ReadAsync 3121", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729510228, "dur": 1, "ph": "X", "name": "ProcessMessages 877", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729510230, "dur": 192, "ph": "X", "name": "ReadAsync 877", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729510425, "dur": 3, "ph": "X", "name": "ProcessMessages 2339", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729510429, "dur": 73, "ph": "X", "name": "ReadAsync 2339", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729510505, "dur": 3, "ph": "X", "name": "ProcessMessages 2271", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729510509, "dur": 186, "ph": "X", "name": "ReadAsync 2271", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729510701, "dur": 362, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729511074, "dur": 316, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729511393, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729511451, "dur": 6, "ph": "X", "name": "ProcessMessages 860", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729511459, "dur": 25, "ph": "X", "name": "ReadAsync 860", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729511488, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729511490, "dur": 21, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729511514, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729511517, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729511550, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729511552, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729511577, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729511580, "dur": 25, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729511607, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729511609, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729511653, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729511677, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729511679, "dur": 19, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729511700, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729511720, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729511737, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729511767, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729511800, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729511802, "dur": 29, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729511834, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729511837, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729511874, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729511876, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729511937, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729511968, "dur": 882, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729512855, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729512891, "dur": 413, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729513306, "dur": 323925, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729837239, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729837243, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729837307, "dur": 1, "ph": "X", "name": "ProcessMessages 21813", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729837312, "dur": 32, "ph": "X", "name": "ReadAsync 21813", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729837345, "dur": 22154, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729859503, "dur": 148, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729859654, "dur": 214, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 3528, "tid": 12884901888, "ts": 1748537729859871, "dur": 101, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 3528, "tid": 25, "ts": 1748537729872866, "dur": 147, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 3528, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 3528, "tid": 8589934592, "ts": 1748537729479047, "dur": 157085, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 3528, "tid": 8589934592, "ts": 1748537729636135, "dur": 3, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 3528, "tid": 8589934592, "ts": 1748537729636139, "dur": 989, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 3528, "tid": 25, "ts": 1748537729873015, "dur": 3, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 3528, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 3528, "tid": 4294967296, "ts": 1748537729455300, "dur": 405503, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 3528, "tid": 4294967296, "ts": 1748537729460051, "dur": 13805, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 3528, "tid": 4294967296, "ts": 1748537729860818, "dur": 5934, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 3528, "tid": 4294967296, "ts": 1748537729863339, "dur": 2311, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 3528, "tid": 4294967296, "ts": 1748537729866818, "dur": 9, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 3528, "tid": 25, "ts": 1748537729873020, "dur": 5, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748537729485051, "dur": 23449, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748537729508510, "dur": 56, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748537729508597, "dur": 338, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748537729509133, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_B3BB0DA1997A2647.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748537729509894, "dur": 229, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_89B4F204A66FCD3B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748537729510353, "dur": 104, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_C9CFE1DD70D57119.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748537729508954, "dur": 1745, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748537729510700, "dur": 330916, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748537729841617, "dur": 555, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748537729842204, "dur": 88, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748537729846452, "dur": 1321, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748537729509366, "dur": 1340, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748537729510735, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748537729510806, "dur": 452, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1748537729510708, "dur": 550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_A67C459F9DC5AEAD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748537729511259, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748537729511357, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748537729511356, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_7933C8582AE17C92.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748537729511427, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748537729511563, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748537729511707, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748537729511958, "dur": 611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748537729512569, "dur": 329038, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748537729510642, "dur": 185, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748537729510876, "dur": 470, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1748537729510829, "dur": 518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_5AF75CBDC7D22D6F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748537729511347, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748537729511502, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748537729511804, "dur": 169, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748537729511803, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_7CC3BC782BC99F3E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748537729511975, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748537729512096, "dur": 126557, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748537729638654, "dur": 202960, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748537729509556, "dur": 1156, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748537729510720, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748537729510802, "dur": 480, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1748537729510714, "dur": 569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_926D2B195BEC1C1F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748537729511284, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748537729511414, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_8ED626F90EA59802.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748537729511465, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748537729511577, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748537729511714, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748537729511861, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748537729511859, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_901B9CE39A8D7C0F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748537729511963, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748537729512020, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748537729512683, "dur": 328964, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748537729509993, "dur": 779, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748537729510830, "dur": 470, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": 1748537729510775, "dur": 526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_DC8E3421511E8CD6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748537729511302, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748537729511459, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748537729511590, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748537729511675, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748537729511674, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_470B0DA014C860E0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748537729511762, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748537729511857, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748537729511856, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_C2F00331D8C496E9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748537729511960, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748537729512047, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748537729512532, "dur": 329071, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748537729509684, "dur": 1033, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748537729510738, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748537729510798, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1748537729510720, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_431E85A617E2A342.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748537729510980, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748537729511093, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748537729511209, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1748537729511171, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_7D7229C02E0DB889.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748537729511278, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748537729511389, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748537729511481, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748537729511618, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748537729511813, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748537729511964, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748537729512553, "dur": 329093, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748537729510141, "dur": 640, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748537729510792, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748537729510873, "dur": 472, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1748537729510784, "dur": 562, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_E3D37FEC8CA3287A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748537729511346, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748537729511455, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PhysicsModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748537729511454, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_6F6E7695D0B1844D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748537729511535, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748537729511636, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_9191F81B58720FCA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748537729511687, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748537729511874, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748537729511966, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748537729512095, "dur": 125342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748537729637438, "dur": 1210, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748537729638648, "dur": 202995, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748537729509843, "dur": 921, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748537729510777, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748537729510857, "dur": 476, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1748537729510768, "dur": 565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_043AF99BA411347E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748537729511334, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748537729511476, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748537729511616, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748537729511736, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748537729511859, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748537729511858, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_0789CA4FD66C3EE4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748537729511961, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748537729512117, "dur": 329515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748537729510320, "dur": 468, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748537729510846, "dur": 470, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 8, "ts": 1748537729510791, "dur": 526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_95E784EC8E0862CB.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748537729511317, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748537729511465, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748537729511594, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748537729511720, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748537729511864, "dur": 136, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748537729511862, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_2C6DDFD7B4B5E2B3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748537729512002, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748537729512114, "dur": 329490, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748537729510459, "dur": 351, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748537729510821, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748537729510880, "dur": 477, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": 1748537729510812, "dur": 546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_94F18CE7015FE34E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748537729511359, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748537729511526, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748537729511756, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748537729511846, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748537729511845, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_132925DC02CDA280.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748537729511971, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748537729512113, "dur": 329535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748537729510656, "dur": 183, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748537729510885, "dur": 471, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 10, "ts": 1748537729510840, "dur": 517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_A4331CFC950ED6D5.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748537729511357, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748537729511642, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748537729511804, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748537729511891, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748537729512100, "dur": 329506, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748537729510721, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748537729510801, "dur": 214, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 11, "ts": 1748537729510707, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_0954F19AD29BDF65.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748537729511017, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748537729511137, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748537729511261, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748537729511385, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748537729511512, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748537729511689, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748537729511855, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748537729511853, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_318BC6ACF69CAB06.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748537729511962, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748537729512109, "dur": 329539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748537729510356, "dur": 439, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748537729510810, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748537729510867, "dur": 471, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 12, "ts": 1748537729510797, "dur": 541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_052C350FFD3D9DA1.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748537729511339, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748537729511492, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748537729511490, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_2A0F5EACF40AB8B7.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748537729511546, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748537729511639, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748537729511848, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748537729511847, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_4639AD57CB8F0BF2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748537729511955, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748537729512101, "dur": 329514, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748537729510819, "dur": 464, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 13, "ts": 1748537729510766, "dur": 518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_169242F115DD75D8.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748537729511285, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748537729511419, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748537729511543, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748537729511684, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748537729511851, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748537729511976, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748537729512701, "dur": 328918, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748537729510861, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_C9CFE1DD70D57119.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748537729510914, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748537729511082, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748537729511172, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748537729511261, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748537729511449, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PropertiesModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1748537729511448, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_52952EA4AE156341.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748537729511557, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748537729511701, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748537729511853, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1748537729511851, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_B3BB0DA1997A2647.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748537729511972, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748537729512241, "dur": 552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RichBillionaire.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1748537729837489, "dur": 150, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748537729513211, "dur": 324637, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/RichBillionaire.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1748537729511011, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748537729511121, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748537729511222, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748537729511322, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748537729511524, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748537729511662, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748537729511807, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748537729511896, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748537729511996, "dur": 616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748537729512612, "dur": 328998, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748537729510938, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748537729511084, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748537729511179, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748537729511284, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748537729511436, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748537729511563, "dur": 311, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748537729511877, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748537729512007, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748537729512660, "dur": 328984, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748537729848987, "dur": 864, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 3528, "tid": 25, "ts": 1748537729873362, "dur": 1654, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 3528, "tid": 25, "ts": 1748537729875102, "dur": 1394, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 3528, "tid": 25, "ts": 1748537729870902, "dur": 6193, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}