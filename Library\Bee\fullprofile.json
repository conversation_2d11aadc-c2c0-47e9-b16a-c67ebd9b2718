{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 3528, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 3528, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 3528, "tid": 295, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 3528, "tid": 295, "ts": 1748541648588904, "dur": 12, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 3528, "tid": 295, "ts": 1748541648588936, "dur": 6, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 3528, "tid": 81604378624, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647667504, "dur": 17207, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647684712, "dur": 903534, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647684724, "dur": 27, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647684754, "dur": 19411, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647704178, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647704184, "dur": 116, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647704303, "dur": 5, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647704309, "dur": 1177, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647705497, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647705500, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647705554, "dur": 1, "ph": "X", "name": "ProcessMessages 1350", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647705557, "dur": 34, "ph": "X", "name": "ReadAsync 1350", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647705594, "dur": 1, "ph": "X", "name": "ProcessMessages 782", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647705598, "dur": 100, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647705701, "dur": 1, "ph": "X", "name": "ProcessMessages 971", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647705704, "dur": 47, "ph": "X", "name": "ReadAsync 971", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647705754, "dur": 2, "ph": "X", "name": "ProcessMessages 2142", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647705757, "dur": 44, "ph": "X", "name": "ReadAsync 2142", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647705803, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647705837, "dur": 1, "ph": "X", "name": "ProcessMessages 957", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647705839, "dur": 35, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647705877, "dur": 37, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647705917, "dur": 1, "ph": "X", "name": "ProcessMessages 793", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647705920, "dur": 35, "ph": "X", "name": "ReadAsync 793", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647705957, "dur": 1, "ph": "X", "name": "ProcessMessages 883", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647705958, "dur": 25, "ph": "X", "name": "ReadAsync 883", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647705985, "dur": 29, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647706017, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647706019, "dur": 67, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647706088, "dur": 1, "ph": "X", "name": "ProcessMessages 682", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647706089, "dur": 24, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647706116, "dur": 1, "ph": "X", "name": "ProcessMessages 848", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647706117, "dur": 32, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647706152, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647706154, "dur": 34, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647706190, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647706192, "dur": 33, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647706228, "dur": 1, "ph": "X", "name": "ProcessMessages 799", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647706229, "dur": 25, "ph": "X", "name": "ReadAsync 799", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647706256, "dur": 22, "ph": "X", "name": "ReadAsync 801", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647706282, "dur": 27, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647706311, "dur": 1, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647706312, "dur": 24, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647706340, "dur": 30, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647706372, "dur": 1, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647706374, "dur": 82, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647706459, "dur": 1, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647706461, "dur": 49, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647706511, "dur": 1, "ph": "X", "name": "ProcessMessages 3156", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647706516, "dur": 28, "ph": "X", "name": "ReadAsync 3156", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647706547, "dur": 1, "ph": "X", "name": "ProcessMessages 890", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647706550, "dur": 27, "ph": "X", "name": "ReadAsync 890", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647706579, "dur": 1, "ph": "X", "name": "ProcessMessages 950", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647706581, "dur": 21, "ph": "X", "name": "ReadAsync 950", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647706604, "dur": 7, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647706612, "dur": 23, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647706638, "dur": 90, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647706731, "dur": 1, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647706733, "dur": 44, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647706779, "dur": 1, "ph": "X", "name": "ProcessMessages 3273", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647706782, "dur": 85, "ph": "X", "name": "ReadAsync 3273", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647706869, "dur": 1, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647706871, "dur": 45, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647706918, "dur": 1, "ph": "X", "name": "ProcessMessages 2366", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647706921, "dur": 26, "ph": "X", "name": "ReadAsync 2366", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647706949, "dur": 1, "ph": "X", "name": "ProcessMessages 787", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647706950, "dur": 79, "ph": "X", "name": "ReadAsync 787", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647707032, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647707034, "dur": 78, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647707115, "dur": 1, "ph": "X", "name": "ProcessMessages 1731", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647707118, "dur": 31, "ph": "X", "name": "ReadAsync 1731", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647707150, "dur": 1, "ph": "X", "name": "ProcessMessages 831", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647707151, "dur": 74, "ph": "X", "name": "ReadAsync 831", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647707229, "dur": 40, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647707272, "dur": 1, "ph": "X", "name": "ProcessMessages 1891", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647707274, "dur": 31, "ph": "X", "name": "ReadAsync 1891", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647707307, "dur": 1, "ph": "X", "name": "ProcessMessages 898", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647707309, "dur": 23, "ph": "X", "name": "ReadAsync 898", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647707335, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647707337, "dur": 22, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647707361, "dur": 21, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647707385, "dur": 25, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647707412, "dur": 21, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647707435, "dur": 23, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647707461, "dur": 19, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647707483, "dur": 19, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647707504, "dur": 19, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647707526, "dur": 21, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647707549, "dur": 18, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647707571, "dur": 19, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647707592, "dur": 310, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647707907, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647707932, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647707934, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647707959, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647707961, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647707984, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708009, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708011, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708044, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708046, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708074, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708077, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708113, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708149, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708151, "dur": 24, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708178, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708180, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708203, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708225, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708227, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708250, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708252, "dur": 27, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708281, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708284, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708312, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708315, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708345, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708347, "dur": 26, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708376, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708378, "dur": 20, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708401, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708432, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708435, "dur": 81, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708520, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708541, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708565, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708568, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708592, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708594, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708625, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708627, "dur": 25, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708655, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708657, "dur": 31, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708691, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708693, "dur": 32, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708728, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708731, "dur": 156, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708891, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708919, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708921, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708948, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647708950, "dur": 675, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647709628, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647709630, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647709659, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647709663, "dur": 704, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647710372, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647710403, "dur": 28, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647710432, "dur": 380, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647710818, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647710851, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647710852, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647710883, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647710884, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647710934, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647710954, "dur": 280, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647711238, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647711269, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647711270, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647711304, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647711327, "dur": 99, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647711429, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647711453, "dur": 419, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647711876, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647711907, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647711943, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647711971, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647711973, "dur": 108, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647712083, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647712102, "dur": 471, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647712580, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647712601, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647712622, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647712669, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647712686, "dur": 6942, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647719634, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647719638, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647719660, "dur": 7, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541647719668, "dur": 858165, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541648577842, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541648577845, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541648577894, "dur": 1292, "ph": "X", "name": "ProcessMessages 5135", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541648579189, "dur": 5190, "ph": "X", "name": "ReadAsync 5135", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541648584386, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541648584390, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541648584410, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 3528, "tid": 81604378624, "ts": 1748541648584412, "dur": 3827, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 3528, "tid": 295, "ts": 1748541648588945, "dur": 541, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 3528, "tid": 77309411328, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 3528, "tid": 77309411328, "ts": 1748541647667477, "dur": 5, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 3528, "tid": 77309411328, "ts": 1748541647667483, "dur": 17218, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 3528, "tid": 77309411328, "ts": 1748541647684703, "dur": 28, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 3528, "tid": 295, "ts": 1748541648589488, "dur": 8, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 3528, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 3528, "tid": 1, "ts": 1748541647433076, "dur": 902, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 3528, "tid": 1, "ts": 1748541647433981, "dur": 8069, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 3528, "tid": 1, "ts": 1748541647442052, "dur": 9959, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 3528, "tid": 295, "ts": 1748541648589498, "dur": 6, "ph": "X", "name": "", "args": {}}, {"pid": 3528, "tid": 73014444032, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647433045, "dur": 13982, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647447028, "dur": 11492, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647447036, "dur": 24, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647447061, "dur": 8, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647447070, "dur": 470, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647447682, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647447684, "dur": 3161, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647450850, "dur": 7, "ph": "X", "name": "ProcessMessages 11029", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647450859, "dur": 225, "ph": "X", "name": "ReadAsync 11029", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647451087, "dur": 10, "ph": "X", "name": "ProcessMessages 20552", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647451098, "dur": 40, "ph": "X", "name": "ReadAsync 20552", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647451144, "dur": 9, "ph": "X", "name": "ProcessMessages 1331", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647451155, "dur": 45, "ph": "X", "name": "ReadAsync 1331", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647451202, "dur": 1, "ph": "X", "name": "ProcessMessages 906", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647451204, "dur": 22, "ph": "X", "name": "ReadAsync 906", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647451229, "dur": 21, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647451252, "dur": 20, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647451275, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647451277, "dur": 22, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647451302, "dur": 24, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647451327, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647451329, "dur": 27, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647451358, "dur": 285, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647451645, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647451648, "dur": 102, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647451753, "dur": 5, "ph": "X", "name": "ProcessMessages 4405", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647451759, "dur": 264, "ph": "X", "name": "ReadAsync 4405", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452028, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452048, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452050, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452099, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452129, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452132, "dur": 23, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452159, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452161, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452185, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452186, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452209, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452210, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452232, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452234, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452250, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452270, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452272, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452294, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452315, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452336, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452338, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452362, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452364, "dur": 20, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452388, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452411, "dur": 19, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452432, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452434, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452456, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452458, "dur": 13, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452474, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452573, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452575, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452607, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452609, "dur": 23, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452636, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452638, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452673, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452675, "dur": 27, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452706, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452760, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452786, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452788, "dur": 27, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452818, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452820, "dur": 29, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452851, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452854, "dur": 24, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452880, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452882, "dur": 21, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452905, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452907, "dur": 20, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452932, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647452957, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647453001, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647453032, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647453034, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647453064, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647453086, "dur": 1857, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647454948, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647454975, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647455004, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647455024, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 3528, "tid": 73014444032, "ts": 1748541647455025, "dur": 3487, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 3528, "tid": 295, "ts": 1748541648589506, "dur": 136, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 3528, "tid": 68719476736, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 3528, "tid": 68719476736, "ts": 1748541647433006, "dur": 19015, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 3528, "tid": 68719476736, "ts": 1748541647452021, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 3528, "tid": 68719476736, "ts": 1748541647452023, "dur": 410, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 3528, "tid": 295, "ts": 1748541648589644, "dur": 5, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 3528, "tid": 64424509440, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 3528, "tid": 64424509440, "ts": 1748541647430564, "dur": 27996, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 3528, "tid": 64424509440, "ts": 1748541647430716, "dur": 1943, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 3528, "tid": 64424509440, "ts": 1748541647458567, "dur": 206511, "ph": "X", "name": "await ExecuteBuildProgram", "args": {}}, {"pid": 3528, "tid": 64424509440, "ts": 1748541647665133, "dur": 923153, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 3528, "tid": 64424509440, "ts": 1748541647665228, "dur": 2101, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 3528, "tid": 64424509440, "ts": 1748541648588290, "dur": 56, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 3528, "tid": 64424509440, "ts": 1748541648588308, "dur": 18, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 3528, "tid": 64424509440, "ts": 1748541648588348, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 3528, "tid": 295, "ts": 1748541648589651, "dur": 50, "ph": "X", "name": "BuildAsync", "args": {}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "netcorerun.dll"}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 35942, "tid": 1, "ts": 1748541647514273, "dur": 128466, "ph": "X", "name": "BuildProgram", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1748541647515196, "dur": 41216, "ph": "X", "name": "BuildProgramContextConstructor", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1748541647612053, "dur": 5624, "ph": "X", "name": "OutputData.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1748541647617683, "dur": 25025, "ph": "X", "name": "Backend.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1748541647619781, "dur": 20621, "ph": "X", "name": "JsonToString", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1748541647653063, "dur": 1748, "ph": "X", "name": "", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1748541647652420, "dur": 2909, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748541647446286, "dur": 934, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748541647447230, "dur": 72, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748541647447342, "dur": 352, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748541647448142, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_96E35984CAD2E0EB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748541647448856, "dur": 2478, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_B799F8A1111E0279.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748541647447710, "dur": 4180, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748541647451892, "dur": 2226, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748541647454119, "dur": 127, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748541647454257, "dur": 533, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748541647454818, "dur": 277, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748541647455146, "dur": 88, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748541647455273, "dur": 993, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748541647447763, "dur": 4140, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748541647451931, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748541647452002, "dur": 234, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1748541647451916, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_052C350FFD3D9DA1.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748541647452367, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748541647452366, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_2C6DDFD7B4B5E2B3.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748541647452690, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748541647452688, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_8AA433C90050CAC8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748541647452805, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748541647452804, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_040BEB3B4DCBE197.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748541647452910, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748541647452909, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_0E8B52DF278607E5.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748541647453150, "dur": 1055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748541647447838, "dur": 4095, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748541647451945, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748541647452006, "dur": 342, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1748541647451936, "dur": 413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_958DBB398EA19D56.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748541647452350, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748541647452443, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748541647452442, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_E3D37FEC8CA3287A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748541647452539, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748541647452593, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_7CF00AAB3B07EE8A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748541647452650, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748541647452707, "dur": 312, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ScreenCaptureModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748541647452706, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_605FAFF9AD97DAD0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748541647453022, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748541647453090, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748541647453157, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748541647453298, "dur": 799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748541647454098, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748541647447804, "dur": 4112, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748541647451934, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748541647452004, "dur": 305, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1748541647451920, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_D60F4678B0C9138A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748541647452358, "dur": 1752, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748541647447829, "dur": 4094, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748541647451939, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748541647452032, "dur": 355, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": 1748541647451927, "dur": 461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_FB0CA54B74E90E14.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748541647452436, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748541647452434, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_169242F115DD75D8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748541647452551, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_E33771651DE4D565.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748541647452602, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748541647452661, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748541647452660, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_9188A12F15CB614B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748541647452715, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748541647452785, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748541647452783, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_E1B14D67211F502C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748541647452935, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748541647453200, "dur": 939, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748541647447914, "dur": 4026, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748541647451952, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748541647452017, "dur": 303, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1748541647451944, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_E660793899F04DE4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748541647452733, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748541647452791, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.JSONSerializeModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748541647452789, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_46780274992BAE6E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748541647452919, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748541647452918, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_470B0DA014C860E0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748541647453163, "dur": 891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748541647454054, "dur": 949, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748541647447944, "dur": 4004, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748541647451963, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748541647452028, "dur": 351, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1748541647451953, "dur": 427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_6C261F44AB06F2F8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748541647452380, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748541647452538, "dur": 130, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748541647452536, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_E2964BE09A8A93C0.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748541647452671, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748541647452793, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748541647452893, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748541647453032, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748541647453133, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748541647453989, "dur": 929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748541647447975, "dur": 3982, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748541647451974, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748541647452026, "dur": 334, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1748541647451960, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_2774BB72C3E9800F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748541647452411, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748541647452682, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_7933C8582AE17C92.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748541647452786, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748541647452785, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_E02FF5D4853CF3B3.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748541647452928, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748541647453141, "dur": 887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748541647454029, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748541647447997, "dur": 3968, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748541647452024, "dur": 312, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 8, "ts": 1748541647451968, "dur": 369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_7CC3BC782BC99F3E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748541647452467, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748541647452465, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_431E85A617E2A342.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748541647452561, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748541647452787, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748541647452926, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748541647452925, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7574F99E59035FF8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748541647453076, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748541647453129, "dur": 904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748541647454033, "dur": 939, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748541647448030, "dur": 3947, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748541647451996, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748541647452056, "dur": 407, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": 1748541647451980, "dur": 484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_5B115C4D92515B9E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748541647452464, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748541647452792, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748541647452791, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_46987855B0E3A6A0.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748541647452912, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748541647452911, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_9819ED8ACBA729A3.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748541647453131, "dur": 875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748541647454006, "dur": 924, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748541647448052, "dur": 3930, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748541647452040, "dur": 353, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 10, "ts": 1748541647451984, "dur": 410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_132925DC02CDA280.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748541647452438, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748541647452437, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_DC8E3421511E8CD6.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748541647452664, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_5985A353A58EF3A3.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748541647452783, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.Physics2DModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748541647452781, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_22B5366951C60B1F.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748541647452925, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748541647453135, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748541647453197, "dur": 867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748541647454064, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748541647448085, "dur": 3944, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748541647452087, "dur": 319, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 11, "ts": 1748541647452031, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_4639AD57CB8F0BF2.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748541647452408, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748541647452722, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748541647452789, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748541647452787, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_2A0F5EACF40AB8B7.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748541647452861, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748541647452923, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748541647452922, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_E022B0A53AAB81FC.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748541647453159, "dur": 1092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748541647448104, "dur": 3937, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748541647452053, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748541647452117, "dur": 299, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 12, "ts": 1748541647452044, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_B3BB0DA1997A2647.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748541647452445, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748541647452708, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_FC0A74906F4439E8.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748541647452814, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.HotReloadModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748541647452812, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_5758047E8BFC2F0C.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748541647452917, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748541647452916, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_EBC2945A5C8EC5BE.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748541647453081, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748541647453137, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748541647453973, "dur": 912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748541647448126, "dur": 3922, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748541647452064, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748541647452162, "dur": 312, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 13, "ts": 1748541647452052, "dur": 424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_318BC6ACF69CAB06.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748541647452476, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748541647452627, "dur": 160, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748541647452625, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_899F220464CF5AC5.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748541647452790, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748541647452906, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748541647453027, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748541647453025, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_690369FE7ACF6B74.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748541647453096, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748541647453167, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748541647453462, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\mscorlib.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748541647453739, "dur": 281, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748541647454110, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748541647454180, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748541647454336, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Windows.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748541647454600, "dur": 144, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748541647453331, "dur": 1804, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1748541647455136, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748541647448143, "dur": 3915, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748541647452071, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1748541647452180, "dur": 295, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 14, "ts": 1748541647452061, "dur": 415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_8ED626F90EA59802.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748541647452476, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748541647452629, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748541647452732, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748541647452798, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1748541647452797, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_06BA14BCF0F515E9.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748541647452945, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 14, "ts": 1748541647452943, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_043AF99BA411347E.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748541647453155, "dur": 1189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748541647448166, "dur": 3900, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748541647452123, "dur": 342, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 15, "ts": 1748541647452068, "dur": 398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_C2F00331D8C496E9.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1748541647452466, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748541647452713, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PropertiesModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1748541647452711, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_52952EA4AE156341.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1748541647452825, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_43B074E6D83CA464.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1748541647452876, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748541647452941, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1748541647452939, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_4F00803CEBBEFC1F.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1748541647453148, "dur": 942, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748541647454091, "dur": 799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748541647448190, "dur": 3883, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748541647452127, "dur": 329, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 16, "ts": 1748541647452073, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_0789CA4FD66C3EE4.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1748541647452738, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748541647452799, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1748541647452798, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_1882500C00DD15DD.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1748541647452906, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CoreModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1748541647452904, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_663F58F8BFE80E6D.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1748541647453135, "dur": 827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748541647453963, "dur": 933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748541647457621, "dur": 710, "ph": "X", "name": "ProfilerWriteOutput"}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748541647684287, "dur": 20211, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748541647704507, "dur": 64, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748541647704610, "dur": 444, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748541647705079, "dur": 2136, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748541647707216, "dur": 875260, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748541648582478, "dur": 350, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748541648582828, "dur": 56, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748541648583984, "dur": 1545, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748541647705101, "dur": 2122, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748541647707226, "dur": 2085, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748541647709311, "dur": 811, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748541647710123, "dur": 346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748541647710469, "dur": 422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748541647710892, "dur": 630, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748541647711522, "dur": 680, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748541647712203, "dur": 870312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748541647705160, "dur": 2086, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748541647707259, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748541647707318, "dur": 213, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1748541647707249, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_FB0CA54B74E90E14.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748541647707649, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748541647707648, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_95E784EC8E0862CB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748541647707790, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TilemapModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748541647707789, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_28B15985BE2CAE44.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748541647708066, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748541647708172, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1748541647708065, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_EBC2945A5C8EC5BE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748541647708300, "dur": 1167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748541647709467, "dur": 986, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748541647710454, "dur": 422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748541647710877, "dur": 640, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748541647711517, "dur": 673, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748541647712190, "dur": 870368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748541647705128, "dur": 2101, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748541647707247, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748541647707320, "dur": 226, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1748541647707239, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_052C350FFD3D9DA1.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748541647707613, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748541647708061, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748541647708060, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_9819ED8ACBA729A3.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748541647708171, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748541647708244, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748541647708298, "dur": 912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748541647709211, "dur": 925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748541647710136, "dur": 319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748541647710456, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748541647710931, "dur": 595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748541647711527, "dur": 679, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748541647712206, "dur": 870299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748541647705153, "dur": 2087, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748541647707255, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748541647707315, "dur": 170, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": 1748541647707243, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_D60F4678B0C9138A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748541647707602, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748541647707601, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_A67C459F9DC5AEAD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748541647707681, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748541647707750, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748541647707749, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_E2964BE09A8A93C0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748541647707807, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748541647707880, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748541647707879, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_0E8661F65C99947F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748541647707985, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748541647708068, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748541647708067, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_470B0DA014C860E0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748541647708172, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748541647708306, "dur": 1094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748541647709401, "dur": 544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748541647709945, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748541647710458, "dur": 421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748541647710879, "dur": 634, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748541647711560, "dur": 633, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748541647712193, "dur": 870352, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748541647705192, "dur": 2061, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748541647707263, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748541647707322, "dur": 234, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1748541647707255, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_958DBB398EA19D56.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748541647707688, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748541647707999, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_2559EF663B29FF05.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748541647708050, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748541647708291, "dur": 815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748541647709107, "dur": 894, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748541647710001, "dur": 453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748541647710455, "dur": 431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748541647710886, "dur": 648, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748541647711534, "dur": 662, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748541647712196, "dur": 870343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748541647705223, "dur": 2035, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748541647707271, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748541647707336, "dur": 250, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1748541647707261, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_E660793899F04DE4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748541647707628, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748541647707626, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_169242F115DD75D8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748541647707686, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748541647707760, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748541647707759, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_FFCF669E316445AF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748541647707861, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_674F48124BA3C8D6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748541647708086, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748541647708174, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748541647708173, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_690369FE7ACF6B74.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748541647708272, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748541647708440, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748541647709764, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748541647709818, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748541647708551, "dur": 1809, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748541647710361, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748541647710560, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748541647710794, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748541647710930, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748541647711053, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748541647711419, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748541647711578, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748541647711714, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748541647712074, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748541647712482, "dur": 6670, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748541647712295, "dur": 6858, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748541647719157, "dur": 57, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748541647719225, "dur": 858216, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748541647705257, "dur": 2009, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748541647707275, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748541647707331, "dur": 247, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1748541647707269, "dur": 310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_6C261F44AB06F2F8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748541647707626, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748541647707625, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_926D2B195BEC1C1F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748541647707689, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748541647707776, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748541647707775, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_5AF75CBDC7D22D6F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748541647707857, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748541647707930, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.Physics2DModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748541647707929, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_22B5366951C60B1F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748541647708064, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748541647708293, "dur": 921, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748541647709214, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748541647710123, "dur": 341, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748541647710464, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748541647710890, "dur": 637, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748541647711528, "dur": 664, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748541647712192, "dur": 870279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748541647705291, "dur": 1981, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748541647707330, "dur": 236, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 8, "ts": 1748541647707275, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_2774BB72C3E9800F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748541647707611, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748541647707690, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_A4331CFC950ED6D5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748541647707868, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748541647707867, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_8AA433C90050CAC8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748541647707920, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748541647707989, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748541647707988, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_43B074E6D83CA464.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748541647708057, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748541647708183, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748541647708321, "dur": 979, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748541647709301, "dur": 910, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748541647710211, "dur": 247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748541647710458, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748541647710890, "dur": 630, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748541647711520, "dur": 671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748541647712191, "dur": 870282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748541647705314, "dur": 1965, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748541647707333, "dur": 264, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": 1748541647707282, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_7CC3BC782BC99F3E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748541647707676, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748541647707749, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748541647707748, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_59ADA145A78A1AC4.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748541647708105, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748541647708104, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_4F00803CEBBEFC1F.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748541647708269, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748541647708437, "dur": 1126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748541647709564, "dur": 888, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748541647710499, "dur": 384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748541647710884, "dur": 632, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748541647711516, "dur": 672, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748541647712232, "dur": 870253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748541647705344, "dur": 1941, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748541647707342, "dur": 270, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 10, "ts": 1748541647707289, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_5B115C4D92515B9E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748541647707613, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748541647707869, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_5C12776C4924B818.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748541647708096, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748541647709482, "dur": 529, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1748541647710012, "dur": 464, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748541647710476, "dur": 405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748541647710881, "dur": 642, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748541647711523, "dur": 685, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748541647712209, "dur": 870289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748541647705380, "dur": 1913, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748541647707304, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748541647707388, "dur": 295, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 11, "ts": 1748541647707297, "dur": 387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_132925DC02CDA280.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748541647707684, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748541647707759, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748541647707757, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_5985A353A58EF3A3.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748541647707815, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748541647707982, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_040BEB3B4DCBE197.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748541647708088, "dur": 207, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748541647708087, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7574F99E59035FF8.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748541647708341, "dur": 1081, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748541647709460, "dur": 996, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748541647710457, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748541647710933, "dur": 585, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748541647711518, "dur": 669, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748541647712191, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748541647712246, "dur": 870232, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748541647705410, "dur": 1891, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748541647707360, "dur": 267, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 12, "ts": 1748541647707304, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_4639AD57CB8F0BF2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748541647707629, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748541647707680, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_4639AD57CB8F0BF2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748541647707737, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748541647707736, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_C9CFE1DD70D57119.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748541647707863, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748541647707953, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748541647707952, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_E02FF5D4853CF3B3.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748541647708047, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748541647708184, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748541647708323, "dur": 918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748541647709242, "dur": 912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748541647710155, "dur": 305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748541647710461, "dur": 419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748541647710880, "dur": 635, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748541647711515, "dur": 683, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748541647712198, "dur": 870333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748541647705428, "dur": 1880, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748541647707319, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748541647707389, "dur": 292, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 13, "ts": 1748541647707311, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_B3BB0DA1997A2647.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748541647707682, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748541647707787, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748541647707851, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748541647707849, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_9188A12F15CB614B.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748541647707972, "dur": 207, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1748541647707971, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_06BA14BCF0F515E9.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1748541647708301, "dur": 852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748541647709154, "dur": 1069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748541647710224, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748541647710474, "dur": 411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748541647710885, "dur": 627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748541647711562, "dur": 637, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1748541647712199, "dur": 870311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748541647705457, "dur": 1857, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748541647707327, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1748541647707378, "dur": 262, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 14, "ts": 1748541647707317, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_318BC6ACF69CAB06.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748541647707688, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1748541647707687, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_0954F19AD29BDF65.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748541647708069, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1748541647708068, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_E022B0A53AAB81FC.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1748541647708177, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748541647708252, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748541647708315, "dur": 856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748541647709172, "dur": 945, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748541647710117, "dur": 334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748541647710501, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748541647710878, "dur": 639, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748541647711517, "dur": 672, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1748541647712190, "dur": 870331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748541647705479, "dur": 1842, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748541647707331, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1748541647707395, "dur": 312, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 15, "ts": 1748541647707322, "dur": 386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_8ED626F90EA59802.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1748541647707761, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_7CF00AAB3B07EE8A.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1748541647707856, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_89B4F204A66FCD3B.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1748541647708095, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748541647708284, "dur": 815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748541647709099, "dur": 938, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748541647710037, "dur": 434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748541647710471, "dur": 426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748541647710897, "dur": 631, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748541647711528, "dur": 683, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1748541647712211, "dur": 870280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748541647705520, "dur": 1820, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748541647707399, "dur": 286, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 16, "ts": 1748541647707341, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_C2F00331D8C496E9.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1748541647707686, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748541647707755, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityCurlModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1748541647707754, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_092BE7AA20DD0577.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1748541647708085, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748541647708308, "dur": 804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748541647709113, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748541647710022, "dur": 444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748541647710466, "dur": 430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748541647710896, "dur": 624, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748541647711521, "dur": 678, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1748541647712199, "dur": 870327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748541648586741, "dur": 774, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 3528, "tid": 295, "ts": 1748541648589763, "dur": 20, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"}}, {"pid": 3528, "tid": 295, "ts": 1748541648590039, "dur": 436, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 3528, "tid": 295, "ts": 1748541648590991, "dur": 34, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend2.traceevents"}}, {"pid": 3528, "tid": 295, "ts": 1748541648589862, "dur": 177, "ph": "X", "name": "buildprogram0.traceevents", "args": {}}, {"pid": 3528, "tid": 295, "ts": 1748541648590548, "dur": 443, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 3528, "tid": 295, "ts": 1748541648591083, "dur": 11551, "ph": "X", "name": "backend2.traceevents", "args": {}}, {"pid": 3528, "tid": 295, "ts": 1748541648588918, "dur": 13786, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}