{"msg":"init","dagFile":"Library/Bee/1900b0aE.dag","targets":["ScriptAssemblies"]}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"ScriptAssemblies","enqueuedNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_690369FE7ACF6B74.mvfrm","enqueuedNodeIndex":5,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_431E85A617E2A342.mvfrm","enqueuedNodeIndex":7,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_926D2B195BEC1C1F.mvfrm","enqueuedNodeIndex":8,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_A67C459F9DC5AEAD.mvfrm","enqueuedNodeIndex":9,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_2C6DDFD7B4B5E2B3.mvfrm","enqueuedNodeIndex":10,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_901B9CE39A8D7C0F.mvfrm","enqueuedNodeIndex":11,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_0789CA4FD66C3EE4.mvfrm","enqueuedNodeIndex":12,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_C2F00331D8C496E9.mvfrm","enqueuedNodeIndex":13,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_318BC6ACF69CAB06.mvfrm","enqueuedNodeIndex":14,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_B3BB0DA1997A2647.mvfrm","enqueuedNodeIndex":15,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_4639AD57CB8F0BF2.mvfrm","enqueuedNodeIndex":16,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_132925DC02CDA280.mvfrm","enqueuedNodeIndex":17,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_5B115C4D92515B9E.mvfrm","enqueuedNodeIndex":18,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_5C12776C4924B818.mvfrm","enqueuedNodeIndex":19,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_7CC3BC782BC99F3E.mvfrm","enqueuedNodeIndex":20,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_2774BB72C3E9800F.mvfrm","enqueuedNodeIndex":21,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_6C261F44AB06F2F8.mvfrm","enqueuedNodeIndex":22,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_E660793899F04DE4.mvfrm","enqueuedNodeIndex":23,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_958DBB398EA19D56.mvfrm","enqueuedNodeIndex":24,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_FB0CA54B74E90E14.mvfrm","enqueuedNodeIndex":25,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_D60F4678B0C9138A.mvfrm","enqueuedNodeIndex":26,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_4F00803CEBBEFC1F.mvfrm","enqueuedNodeIndex":27,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7574F99E59035FF8.mvfrm","enqueuedNodeIndex":28,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_E022B0A53AAB81FC.mvfrm","enqueuedNodeIndex":29,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_470B0DA014C860E0.mvfrm","enqueuedNodeIndex":30,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_EBC2945A5C8EC5BE.mvfrm","enqueuedNodeIndex":31,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_9819ED8ACBA729A3.mvfrm","enqueuedNodeIndex":32,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_0E8B52DF278607E5.mvfrm","enqueuedNodeIndex":33,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_663F58F8BFE80E6D.mvfrm","enqueuedNodeIndex":34,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_8D1DAC88172B6825.mvfrm","enqueuedNodeIndex":35,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_9191F81B58720FCA.mvfrm","enqueuedNodeIndex":36,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_5F1226B013296F98.mvfrm","enqueuedNodeIndex":37,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_96E35984CAD2E0EB.mvfrm","enqueuedNodeIndex":38,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_2559EF663B29FF05.mvfrm","enqueuedNodeIndex":39,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_2DCA04472A508570.mvfrm","enqueuedNodeIndex":40,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_43B074E6D83CA464.mvfrm","enqueuedNodeIndex":41,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_5758047E8BFC2F0C.mvfrm","enqueuedNodeIndex":42,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_040BEB3B4DCBE197.mvfrm","enqueuedNodeIndex":43,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_1882500C00DD15DD.mvfrm","enqueuedNodeIndex":44,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_06BA14BCF0F515E9.mvfrm","enqueuedNodeIndex":45,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_46987855B0E3A6A0.mvfrm","enqueuedNodeIndex":46,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_46780274992BAE6E.mvfrm","enqueuedNodeIndex":47,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_2A0F5EACF40AB8B7.mvfrm","enqueuedNodeIndex":48,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_E02FF5D4853CF3B3.mvfrm","enqueuedNodeIndex":49,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_E1B14D67211F502C.mvfrm","enqueuedNodeIndex":50,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_22B5366951C60B1F.mvfrm","enqueuedNodeIndex":51,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_6F6E7695D0B1844D.mvfrm","enqueuedNodeIndex":52,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_C3D83151F1CB96D7.mvfrm","enqueuedNodeIndex":53,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_52952EA4AE156341.mvfrm","enqueuedNodeIndex":54,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_FC0A74906F4439E8.mvfrm","enqueuedNodeIndex":55,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_605FAFF9AD97DAD0.mvfrm","enqueuedNodeIndex":56,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_881D2A10C4F9DCE7.mvfrm","enqueuedNodeIndex":57,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_0E8661F65C99947F.mvfrm","enqueuedNodeIndex":58,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_8ED626F90EA59802.mvfrm","enqueuedNodeIndex":59,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_8AA433C90050CAC8.mvfrm","enqueuedNodeIndex":60,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_674F48124BA3C8D6.mvfrm","enqueuedNodeIndex":61,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_7933C8582AE17C92.mvfrm","enqueuedNodeIndex":62,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_89B4F204A66FCD3B.mvfrm","enqueuedNodeIndex":63,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_E0AA9EA79E7FEE40.mvfrm","enqueuedNodeIndex":64,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_9188A12F15CB614B.mvfrm","enqueuedNodeIndex":65,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_D970D7A0A54F1A69.mvfrm","enqueuedNodeIndex":66,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_899F220464CF5AC5.mvfrm","enqueuedNodeIndex":67,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_28B15985BE2CAE44.mvfrm","enqueuedNodeIndex":68,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_1412F4782AF279E1.mvfrm","enqueuedNodeIndex":69,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_7D7229C02E0DB889.mvfrm","enqueuedNodeIndex":70,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_91015966EAE8A302.mvfrm","enqueuedNodeIndex":71,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_5AF75CBDC7D22D6F.mvfrm","enqueuedNodeIndex":72,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_7CF00AAB3B07EE8A.mvfrm","enqueuedNodeIndex":73,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_FFCF669E316445AF.mvfrm","enqueuedNodeIndex":74,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_5985A353A58EF3A3.mvfrm","enqueuedNodeIndex":75,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_092BE7AA20DD0577.mvfrm","enqueuedNodeIndex":76,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_E33771651DE4D565.mvfrm","enqueuedNodeIndex":77,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_E2964BE09A8A93C0.mvfrm","enqueuedNodeIndex":78,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_59ADA145A78A1AC4.mvfrm","enqueuedNodeIndex":79,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_15AAF2E558C07C16.mvfrm","enqueuedNodeIndex":80,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_C9CFE1DD70D57119.mvfrm","enqueuedNodeIndex":81,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_A4331CFC950ED6D5.mvfrm","enqueuedNodeIndex":82,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_0954F19AD29BDF65.mvfrm","enqueuedNodeIndex":83,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_94F18CE7015FE34E.mvfrm","enqueuedNodeIndex":84,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_052C350FFD3D9DA1.mvfrm","enqueuedNodeIndex":85,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_95E784EC8E0862CB.mvfrm","enqueuedNodeIndex":86,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_E3D37FEC8CA3287A.mvfrm","enqueuedNodeIndex":87,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_DC8E3421511E8CD6.mvfrm","enqueuedNodeIndex":88,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_169242F115DD75D8.mvfrm","enqueuedNodeIndex":89,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_043AF99BA411347E.mvfrm","enqueuedNodeIndex":90,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/RichBillionaire.dll","enqueuedNodeIndex":93,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/RichBillionaire.dll (+2 others)","enqueuedNodeIndex":4,"enqueueingNodeAnnotation":"CopyFiles Library/ScriptAssemblies/RichBillionaire.dll","enqueueingNodeIndex":93}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/RichBillionaire.UnityAdditionalFile.txt","enqueuedNodeIndex":1,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/RichBillionaire.dll (+2 others)","enqueueingNodeIndex":4}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/RichBillionaire.rsp","enqueuedNodeIndex":2,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/RichBillionaire.dll (+2 others)","enqueueingNodeIndex":4}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/RichBillionaire.rsp2","enqueuedNodeIndex":3,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/RichBillionaire.dll (+2 others)","enqueueingNodeIndex":4}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/RichBillionaire.dll.mvfrm","enqueuedNodeIndex":92,"enqueueingNodeAnnotation":"Csc Library/Bee/artifacts/1900b0aE.dag/RichBillionaire.dll (+2 others)","enqueueingNodeIndex":4}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"WriteText Library/Bee/artifacts/1900b0aE.dag/RichBillionaire.dll.mvfrm.rsp","enqueuedNodeIndex":91,"enqueueingNodeAnnotation":"MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/RichBillionaire.dll.mvfrm","enqueueingNodeIndex":92}
{"msg":"enqueueNode","enqueuedNodeAnnotation":"CopyFiles Library/ScriptAssemblies/RichBillionaire.pdb","enqueuedNodeIndex":94,"enqueueingNodeAnnotation":"ScriptAssemblies","enqueueingNodeIndex":6}
{"msg":"nodeRetryBuild","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/RichBillionaire.dll (+2 others)","index":4}
{"msg":"runNodeAction","annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/RichBillionaire.dll (+2 others)","displayName":"Csc RichBillionaire","index":4}
{"msg":"noderesult","processed_node_count":90,"number_of_nodes_ever_queued":94,"annotation":"Csc Library/Bee/artifacts/1900b0aE.dag/RichBillionaire.dll (+2 others)","index":4,"exitcode":1,"cmdline":"\"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.12f1\\Editor\\Data\\NetCoreRuntime\\dotnet.exe\" exec \"C:/Program Files/Unity/Hub/Editor/2022.3.12f1/Editor/Data/DotNetSdkRoslyn/csc.dll\" /nostdlib /noconfig /shared \"@Library/Bee/artifacts/1900b0aE.dag/RichBillionaire.rsp\" \"@Library/Bee/artifacts/1900b0aE.dag/RichBillionaire.rsp2\"","rsps":["Library\\Bee\\artifacts\\1900b0aE.dag\\RichBillionaire.rsp"],"outputfile":"Library\\Bee\\artifacts\\1900b0aE.dag\\RichBillionaire.dll","stdout":"Assets\\Scripts\\UI\\DashboardUI.cs(2,19): error CS0234: The type or namespace name 'UI' does not exist in the namespace 'UnityEngine' (are you missing an assembly reference?)\r\nAssets\\Scripts\\UI\\DashboardUI.cs(3,7): error CS0246: The type or namespace name 'TMPro' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\PawnNavigationUI.cs(2,19): error CS0234: The type or namespace name 'UI' does not exist in the namespace 'UnityEngine' (are you missing an assembly reference?)\r\nAssets\\Scripts\\UI\\PawnNavigationUI.cs(3,7): error CS0246: The type or namespace name 'TMPro' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\StatsDisplayUI.cs(2,19): error CS0234: The type or namespace name 'UI' does not exist in the namespace 'UnityEngine' (are you missing an assembly reference?)\r\nAssets\\Scripts\\UI\\StatsDisplayUI.cs(3,7): error CS0246: The type or namespace name 'TMPro' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\Zones\\Zone.cs(277,13): error CS0234: The type or namespace name 'SerializableAttribute' does not exist in the namespace 'RichBillionaire.System' (are you missing an assembly reference?)\r\nAssets\\Scripts\\Zones\\Zone.cs(277,13): error CS0234: The type or namespace name 'Serializable' does not exist in the namespace 'RichBillionaire.System' (are you missing an assembly reference?)\r\nAssets\\Scripts\\Zones\\ZoneResourceGenerator.cs(312,13): error CS0234: The type or namespace name 'SerializableAttribute' does not exist in the namespace 'RichBillionaire.System' (are you missing an assembly reference?)\r\nAssets\\Scripts\\System\\EventSystem.cs(476,13): error CS0234: The type or namespace name 'SerializableAttribute' does not exist in the namespace 'RichBillionaire.System' (are you missing an assembly reference?)\r\nAssets\\Scripts\\Zones\\ZoneResourceGenerator.cs(312,13): error CS0234: The type or namespace name 'Serializable' does not exist in the namespace 'RichBillionaire.System' (are you missing an assembly reference?)\r\nAssets\\Scripts\\System\\EventSystem.cs(476,13): error CS0234: The type or namespace name 'Serializable' does not exist in the namespace 'RichBillionaire.System' (are you missing an assembly reference?)\r\nAssets\\Scripts\\System\\GameManager.cs(438,13): error CS0234: The type or namespace name 'SerializableAttribute' does not exist in the namespace 'RichBillionaire.System' (are you missing an assembly reference?)\r\nAssets\\Scripts\\System\\GameManager.cs(438,13): error CS0234: The type or namespace name 'Serializable' does not exist in the namespace 'RichBillionaire.System' (are you missing an assembly reference?)\r\nAssets\\Scripts\\Zones\\ZoneResourceGenerator.cs(272,24): error CS0234: The type or namespace name 'Collections' does not exist in the namespace 'RichBillionaire.System' (are you missing an assembly reference?)\r\nAssets\\Scripts\\Zones\\Zone.cs(234,24): error CS0234: The type or namespace name 'Collections' does not exist in the namespace 'RichBillionaire.System' (are you missing an assembly reference?)\r\nAssets\\Scripts\\System\\GameManager.cs(403,50): error CS0234: The type or namespace name 'Collections' does not exist in the namespace 'RichBillionaire.System' (are you missing an assembly reference?)\r\nAssets\\Scripts\\UI\\DashboardUI.cs(17,34): error CS0246: The type or namespace name 'TextMeshProUGUI' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\Zones\\Zone.cs(40,23): error CS0234: The type or namespace name 'Action<,>' does not exist in the namespace 'RichBillionaire.System' (are you missing an assembly reference?)\r\nAssets\\Scripts\\UI\\DashboardUI.cs(18,34): error CS0246: The type or namespace name 'TextMeshProUGUI' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\PawnNavigationUI.cs(16,34): error CS0246: The type or namespace name 'Button' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\Zones\\Zone.cs(41,23): error CS0234: The type or namespace name 'Action<,>' does not exist in the namespace 'RichBillionaire.System' (are you missing an assembly reference?)\r\nAssets\\Scripts\\UI\\StatsDisplayUI.cs(135,39): error CS0246: The type or namespace name 'Slider' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\StatsDisplayUI.cs(135,54): error CS0246: The type or namespace name 'TextMeshProUGUI' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\Manager\\TaxAndIncomeSystem.cs(53,23): error CS0234: The type or namespace name 'Action<>' does not exist in the namespace 'RichBillionaire.System' (are you missing an assembly reference?)\r\nAssets\\Scripts\\System\\GameManager.cs(43,23): error CS0234: The type or namespace name 'Action<>' does not exist in the namespace 'RichBillionaire.System' (are you missing an assembly reference?)\r\nAssets\\Scripts\\System\\EventSystem.cs(34,23): error CS0234: The type or namespace name 'Action<>' does not exist in the namespace 'RichBillionaire.System' (are you missing an assembly reference?)\r\nAssets\\Scripts\\Manager\\PlayerResourceTracker.cs(55,23): error CS0234: The type or namespace name 'Action<,,>' does not exist in the namespace 'RichBillionaire.System' (are you missing an assembly reference?)\r\nAssets\\Scripts\\Zones\\Zone.cs(42,23): error CS0234: The type or namespace name 'Action<,>' does not exist in the namespace 'RichBillionaire.System' (are you missing an assembly reference?)\r\nAssets\\Scripts\\Manager\\TaxAndIncomeSystem.cs(54,23): error CS0234: The type or namespace name 'Action<>' does not exist in the namespace 'RichBillionaire.System' (are you missing an assembly reference?)\r\nAssets\\Scripts\\System\\GameManager.cs(44,23): error CS0234: The type or namespace name 'Action<>' does not exist in the namespace 'RichBillionaire.System' (are you missing an assembly reference?)\r\nAssets\\Scripts\\System\\EventSystem.cs(35,23): error CS0234: The type or namespace name 'Action<>' does not exist in the namespace 'RichBillionaire.System' (are you missing an assembly reference?)\r\nAssets\\Scripts\\Manager\\PlayerResourceTracker.cs(56,23): error CS0234: The type or namespace name 'Action<>' does not exist in the namespace 'RichBillionaire.System' (are you missing an assembly reference?)\r\nAssets\\Scripts\\Pawns\\PawnGenerator.cs(38,23): error CS0234: The type or namespace name 'Action<>' does not exist in the namespace 'RichBillionaire.System' (are you missing an assembly reference?)\r\nAssets\\Scripts\\UI\\DashboardUI.cs(19,34): error CS0246: The type or namespace name 'TextMeshProUGUI' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\PawnNavigationUI.cs(17,34): error CS0246: The type or namespace name 'Button' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\Manager\\TaxAndIncomeSystem.cs(55,23): error CS0234: The type or namespace name 'Action<>' does not exist in the namespace 'RichBillionaire.System' (are you missing an assembly reference?)\r\nAssets\\Scripts\\Zones\\Zone.cs(43,23): error CS0234: The type or namespace name 'Action<,>' does not exist in the namespace 'RichBillionaire.System' (are you missing an assembly reference?)\r\nAssets\\Scripts\\System\\GameManager.cs(45,23): error CS0234: The type or namespace name 'Action' does not exist in the namespace 'RichBillionaire.System' (are you missing an assembly reference?)\r\nAssets\\Scripts\\Manager\\PlayerResourceTracker.cs(57,23): error CS0234: The type or namespace name 'Action<>' does not exist in the namespace 'RichBillionaire.System' (are you missing an assembly reference?)\r\nAssets\\Scripts\\System\\GameManager.cs(46,23): error CS0234: The type or namespace name 'Action' does not exist in the namespace 'RichBillionaire.System' (are you missing an assembly reference?)\r\nAssets\\Scripts\\System\\GameManager.cs(47,23): error CS0234: The type or namespace name 'Action' does not exist in the namespace 'RichBillionaire.System' (are you missing an assembly reference?)\r\nAssets\\Scripts\\UI\\PawnNavigationUI.cs(18,34): error CS0246: The type or namespace name 'Button' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\StatsDisplayUI.cs(15,34): error CS0246: The type or namespace name 'Slider' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\DashboardUI.cs(20,34): error CS0246: The type or namespace name 'TextMeshProUGUI' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\StatsDisplayUI.cs(16,34): error CS0246: The type or namespace name 'Slider' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\PawnNavigationUI.cs(19,34): error CS0246: The type or namespace name 'Button' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\DashboardUI.cs(21,34): error CS0246: The type or namespace name 'TextMeshProUGUI' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\StatsDisplayUI.cs(17,34): error CS0246: The type or namespace name 'Slider' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\PawnNavigationUI.cs(20,34): error CS0246: The type or namespace name 'TextMeshProUGUI' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\StatsDisplayUI.cs(18,34): error CS0246: The type or namespace name 'Slider' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\DashboardUI.cs(22,34): error CS0246: The type or namespace name 'TextMeshProUGUI' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\StatsDisplayUI.cs(19,34): error CS0246: The type or namespace name 'Slider' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\PawnNavigationUI.cs(23,34): error CS0246: The type or namespace name 'TMP_InputField' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\DashboardUI.cs(25,34): error CS0246: The type or namespace name 'Image' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\StatsDisplayUI.cs(20,34): error CS0246: The type or namespace name 'Slider' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\PawnNavigationUI.cs(24,34): error CS0246: The type or namespace name 'TMP_Dropdown' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\StatsDisplayUI.cs(21,34): error CS0246: The type or namespace name 'Slider' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\DashboardUI.cs(26,34): error CS0246: The type or namespace name 'TextMeshProUGUI' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\PawnNavigationUI.cs(25,34): error CS0246: The type or namespace name 'TMP_Dropdown' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\StatsDisplayUI.cs(22,34): error CS0246: The type or namespace name 'Slider' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\DashboardUI.cs(27,34): error CS0246: The type or namespace name 'TextMeshProUGUI' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\StatsDisplayUI.cs(25,34): error CS0246: The type or namespace name 'TextMeshProUGUI' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\PawnNavigationUI.cs(26,34): error CS0246: The type or namespace name 'Button' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\StatsDisplayUI.cs(26,34): error CS0246: The type or namespace name 'TextMeshProUGUI' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\DashboardUI.cs(28,34): error CS0246: The type or namespace name 'TextMeshProUGUI' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\PawnNavigationUI.cs(29,34): error CS0246: The type or namespace name 'Button' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\StatsDisplayUI.cs(27,34): error CS0246: The type or namespace name 'TextMeshProUGUI' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\PawnNavigationUI.cs(30,34): error CS0246: The type or namespace name 'Button' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\DashboardUI.cs(29,34): error CS0246: The type or namespace name 'TextMeshProUGUI' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\StatsDisplayUI.cs(28,34): error CS0246: The type or namespace name 'TextMeshProUGUI' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\PawnNavigationUI.cs(31,34): error CS0246: The type or namespace name 'Button' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\StatsDisplayUI.cs(29,34): error CS0246: The type or namespace name 'TextMeshProUGUI' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\DashboardUI.cs(32,34): error CS0246: The type or namespace name 'Slider' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\StatsDisplayUI.cs(30,34): error CS0246: The type or namespace name 'TextMeshProUGUI' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\PawnNavigationUI.cs(32,34): error CS0246: The type or namespace name 'Button' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\DashboardUI.cs(33,34): error CS0246: The type or namespace name 'Slider' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\StatsDisplayUI.cs(31,34): error CS0246: The type or namespace name 'TextMeshProUGUI' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\PawnNavigationUI.cs(33,34): error CS0246: The type or namespace name 'Button' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\StatsDisplayUI.cs(32,34): error CS0246: The type or namespace name 'TextMeshProUGUI' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\DashboardUI.cs(34,34): error CS0246: The type or namespace name 'Slider' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\StatsDisplayUI.cs(35,34): error CS0246: The type or namespace name 'Slider' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\PawnNavigationUI.cs(47,23): error CS0234: The type or namespace name 'Action<>' does not exist in the namespace 'RichBillionaire.System' (are you missing an assembly reference?)\r\nAssets\\Scripts\\UI\\StatsDisplayUI.cs(36,34): error CS0246: The type or namespace name 'Slider' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\PawnNavigationUI.cs(48,23): error CS0234: The type or namespace name 'Action<,>' does not exist in the namespace 'RichBillionaire.System' (are you missing an assembly reference?)\r\nAssets\\Scripts\\UI\\DashboardUI.cs(35,34): error CS0246: The type or namespace name 'Slider' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\StatsDisplayUI.cs(37,34): error CS0246: The type or namespace name 'Slider' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\StatsDisplayUI.cs(38,34): error CS0246: The type or namespace name 'Slider' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\StatsDisplayUI.cs(41,34): error CS0246: The type or namespace name 'TextMeshProUGUI' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\DashboardUI.cs(36,34): error CS0246: The type or namespace name 'TextMeshProUGUI' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\StatsDisplayUI.cs(42,34): error CS0246: The type or namespace name 'TextMeshProUGUI' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\StatsDisplayUI.cs(43,34): error CS0246: The type or namespace name 'TextMeshProUGUI' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\StatsDisplayUI.cs(44,34): error CS0246: The type or namespace name 'TextMeshProUGUI' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\DashboardUI.cs(37,34): error CS0246: The type or namespace name 'TextMeshProUGUI' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\StatsDisplayUI.cs(47,34): error CS0246: The type or namespace name 'TextMeshProUGUI' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\StatsDisplayUI.cs(48,34): error CS0246: The type or namespace name 'TextMeshProUGUI' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\DashboardUI.cs(38,34): error CS0246: The type or namespace name 'TextMeshProUGUI' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\StatsDisplayUI.cs(49,34): error CS0246: The type or namespace name 'TextMeshProUGUI' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\StatsDisplayUI.cs(52,34): error CS0246: The type or namespace name 'TextMeshProUGUI' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\StatsDisplayUI.cs(53,34): error CS0246: The type or namespace name 'TextMeshProUGUI' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\DashboardUI.cs(39,34): error CS0246: The type or namespace name 'TextMeshProUGUI' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\StatsDisplayUI.cs(54,34): error CS0246: The type or namespace name 'TextMeshProUGUI' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\DashboardUI.cs(42,34): error CS0246: The type or namespace name 'Button' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\StatsDisplayUI.cs(55,34): error CS0246: The type or namespace name 'TextMeshProUGUI' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\DashboardUI.cs(43,34): error CS0246: The type or namespace name 'Button' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\DashboardUI.cs(44,34): error CS0246: The type or namespace name 'TextMeshProUGUI' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\DashboardUI.cs(47,34): error CS0246: The type or namespace name 'Button' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\DashboardUI.cs(48,34): error CS0246: The type or namespace name 'Button' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\DashboardUI.cs(49,34): error CS0246: The type or namespace name 'Button' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\DashboardUI.cs(50,34): error CS0246: The type or namespace name 'TextMeshProUGUI' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\DashboardUI.cs(53,34): error CS0246: The type or namespace name 'TextMeshProUGUI' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\DashboardUI.cs(54,34): error CS0246: The type or namespace name 'TextMeshProUGUI' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\DashboardUI.cs(55,34): error CS0246: The type or namespace name 'TextMeshProUGUI' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\DashboardUI.cs(58,34): error CS0246: The type or namespace name 'Button' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\DashboardUI.cs(59,34): error CS0246: The type or namespace name 'Button' could not be found (are you missing a using directive or an assembly reference?)\r\nAssets\\Scripts\\UI\\DashboardUI.cs(60,34): error CS0246: The type or namespace name 'Button' could not be found (are you missing a using directive or an assembly reference?)"}
