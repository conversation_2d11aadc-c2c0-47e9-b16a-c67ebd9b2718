# Interaction Components

The [interaction components](UIInteractionComponents.md) in the UI system handle interaction, such as mouse or touch events and interaction using a keyboard or controller.

* [Selectable Base Class](script-Selectable.md)
* [<PERSON><PERSON>](script-Button.md)
* [Toggle](script-Toggle.md)
* [Toggle Group](script-ToggleGroup.md)
* [Slider](script-Slider.md)
* [Scrollbar](script-Scrollbar.md)
* [Scroll Rect](script-ScrollRect.md)
* [InputField](script-InputField.md)
