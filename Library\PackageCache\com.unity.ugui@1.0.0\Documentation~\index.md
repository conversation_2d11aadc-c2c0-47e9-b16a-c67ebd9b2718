# Unity UI: Unity User Interface

Unity UI is a UI toolkit for developing user interfaces for games and applications. It is a GameObject-based UI system that uses Components and the Game View to arrange, position, and style user interfaces.
​
You cannot use Unity UI to create or change user interfaces in the Unity Editor.

This documentation describes Unity UI features such as creating a Canvas, positioning and animating elements, defining user interactions, and sizing layouts automatically.
