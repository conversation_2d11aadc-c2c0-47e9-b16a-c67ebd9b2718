# Physics Raycaster

The Raycaster raycasts against 3D objects in the scene. This allows messages to be sent to 3D physics objects that implement event interfaces.

## Properties

|**_Property:_** |**_Function:_** |
|:---|:---|
|__Depth__ | Get the depth of the configured camera. |
|__Event Camera__ | Get the camera that is used for this module. |
|__Event Mask__ | Logical and of Camera mask and eventMask. |
|__Final Event Mask__ | Logical and of Camera mask and eventMask. |
