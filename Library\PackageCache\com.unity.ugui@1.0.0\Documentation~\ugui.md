# About Unity UI

Unity UI is a UI toolkit for developing user interfaces for games and applications. It is a GameObject-based UI system that uses Components and the Game View to arrange, position, and style user interfaces.

You cannot use Unity UI to create or change user interfaces within the Unity Editor.

## Installing Unity UI

Unity UI is a core package. A version of it is included in each Unity release.

To remove this package, or reinstall it after removal, follow the instructions in the [Package Manager documentation](https://docs.unity3d.com/Manual/upm-ui-actions.html).

## Getting documentation

### User documentation

The Unity UI user documentation is in the [Unity Manual](https://docs.unity3d.com/Manual/UISystem.html). It provides a basic overview of the available components, and a few how-tos.

### API documentation

You can find Class descriptions and API compatibility information in the [Scripting API](../api/index.html) section of this documentation.

## Getting support

For questions and assistance, visit the [Unity UI](https://forum.unity.com/forums/unity-ui-ugui-textmesh-pro.60/) section of the Unity Forum.
