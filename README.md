# Rich Billionaire - Multiplayer Party Strategy Game

## 🎮 Game Overview
Rich Billionaire is an asynchronous multiplayer party-strategy game where 2-8 players secretly control hidden cities on a shared global map. Players influence pawns indirectly through zoning, incentives, relics, miracles, and sabotage.

**Current Status: ✅ MINIMUM VIABLE PRODUCT COMPLETE**
- ✅ Core pawn system with polarity-based stats
- ✅ Zone system with resource generation
- ✅ Player resource tracking and tax system
- ✅ Complete UI with pawn navigation
- ✅ Auto-generation of diverse pawns
- ✅ Event system with random occurrences
- ✅ Extensible architecture for future features

## 🚀 Quick Start Guide

### Prerequisites
- Unity 2022.3 LTS or newer
- Basic understanding of Unity Editor

### Setup Instructions
1. **Clone/Download** this project to your local machine
2. **Open Unity Hub** and click "Add project from disk"
3. **Navigate** to the project folder and select it
4. **Open** the project in Unity (it will import automatically)
5. **Load the Demo Scene**: `Assets/Scenes/DemoScene.unity`
6. **Press Play** to start the game!

### First Run
- The game will auto-generate 20 diverse pawns
- Use arrow buttons to navigate between pawns
- Watch resources generate automatically
- Observe pawn satisfaction and needs change over time
- Press `T` to run system tests
- Press `P` to generate a new pawn
- Press `R` to add test resources

## 🏗 Project Structure
```
Assets/
├── Scenes/
│   └── DemoScene.unity
├── Scripts/
│   ├── System/
│   │   ├── GameManager.cs
│   │   ├── ResourceSystem.cs
│   │   └── EventSystem.cs
│   ├── UI/
│   │   ├── DashboardUI.cs
│   │   ├── PawnNavigationUI.cs
│   │   └── StatsDisplayUI.cs
│   ├── Pawns/
│   │   ├── Pawn.cs
│   │   ├── PawnStats.cs
│   │   ├── PawnGenerator.cs
│   │   └── PawnNameDatabase.cs
│   ├── Zones/
│   │   ├── Zone.cs
│   │   ├── ZoneType.cs
│   │   └── ZoneResourceGenerator.cs
│   └── Manager/
│       ├── PlayerResourceTracker.cs
│       └── TaxAndIncomeSystem.cs
├── Prefabs/
│   ├── UI/
│   └── Pawns/
└── Resources/
    └── Portraits/
```

## 🔧 Core Systems

### Pawn System
- **Pawn.cs**: Core pawn class with stats, needs, satisfaction, desires
- **PawnStats.cs**: Polarity-based attributes (Smart/Dumb, Good/Evil, etc.)
- **PawnGenerator.cs**: Auto-generates diverse starting pawns
- **PawnNameDatabase.cs**: Name pools for different ethnicities

### Zone System
- **Zone.cs**: Base zone functionality
- **ZoneType.cs**: Enum for different zone types (Industrial, Farming, Cultural, etc.)
- **ZoneResourceGenerator.cs**: Resource generation based on pawn stats

### Player System
- **PlayerResourceTracker.cs**: Manages money, materials, influence
- **TaxAndIncomeSystem.cs**: Income collection and tax management

### UI System
- **DashboardUI.cs**: Main player dashboard
- **PawnNavigationUI.cs**: Arrow navigation between pawns
- **StatsDisplayUI.cs**: Displays pawn stats and needs

## 🚀 Getting Started

1. Open the project in Unity 2022 LTS or newer
2. Load the DemoScene
3. Press Play to see auto-generated pawns
4. Use arrow buttons to navigate between pawns
5. Observe resource generation and pawn stats

## 🔮 Future Extensions

### Planned Systems (Hooks Provided)
- **Choice Engine**: Daily pawn decisions with player control
- **Relic System**: Active/passive buffs and artifacts
- **NPC Manipulation**: Riots, boycotts, worship, voting, looting, spying
- **Multiplayer Networking**: Real-time synchronization
- **AI Integration**: Smart pawn behavior and decision making
- **Smartphone Companion**: Mobile interface for remote play

### Extension Points
- `IChoiceEvent` interface for decision events
- `IRelic` interface for relic system
- `INPCAction` interface for NPC behaviors
- `INetworkSync` interface for multiplayer
- `IAIBehavior` interface for AI pawns

## 📱 Smartphone Companion (Future)
The architecture supports future smartphone companion play:
- RESTful API endpoints for game state
- JSON serialization for all game objects
- Event-driven architecture for real-time updates
- Touch-friendly UI components

## 🎨 Art Pipeline
- Portrait system supports both static images and animated characters
- Extensible for AI-generated portraits
- Modular character customization system
- Support for 2D sprites or 3D models

## 🔧 Technical Notes
- Built for Unity 2022+ LTS
- Modular, clean, extensible C# architecture
- Event-driven design for loose coupling
- Serializable data structures for save/load
- Performance optimized for 2-8 players

## 📊 Performance Targets
- 60 FPS on mid-range PC hardware
- < 100MB RAM usage for base game
- < 1 second pawn generation time
- Scalable to 100+ pawns per city

## 🐛 Known Issues & Limitations
- Portrait system currently uses placeholder sprites
- Multiplayer networking not yet implemented
- AI behavior system is placeholder
- Mobile companion app not included

## 📝 Development Roadmap
1. ✅ Core pawn and zone systems
2. ✅ Basic UI and navigation
3. ✅ Resource generation
4. 🔄 Choice engine system
5. 🔄 Relic and artifact system
6. 🔄 NPC manipulation mechanics
7. 🔄 Multiplayer networking
8. 🔄 AI behavior system
9. 🔄 Mobile companion app

## 🧪 Testing & Validation

### Built-in Test System
The project includes a comprehensive test runner (`TestRunner.cs`) that validates all systems:

**Keyboard Shortcuts:**
- `T` - Run all system tests
- `P` - Generate a new pawn
- `R` - Add test resources
- `E` - Trigger random event

**Test Coverage:**
- ✅ Pawn generation and stats validation
- ✅ Resource management operations
- ✅ Zone system functionality
- ✅ Tax and income calculations
- ✅ Event system triggers
- ✅ Name database operations

### Manual Testing Checklist
1. **Pawn System**: Navigate between pawns, observe stat changes
2. **Resource Generation**: Watch money/materials increase over time
3. **Satisfaction System**: See how pawn needs affect overall satisfaction
4. **Zone Effectiveness**: Check how pawn stats affect work performance
5. **Tax Collection**: Observe periodic tax collection events
6. **Random Events**: Wait for random events to trigger (or press `E`)

## 🐛 Troubleshooting

### Common Issues
1. **"No pawns generated"**
   - Check that GameManager is in the scene
   - Verify PawnGenerator component is attached
   - Look for errors in Console window

2. **"UI not updating"**
   - Ensure DashboardUI script is attached to a GameObject
   - Check that UI elements are properly assigned in inspector
   - Verify Canvas is set up correctly

3. **"Resources not generating"**
   - Confirm pawns are assigned to zones
   - Check that ZoneResourceGenerator is working
   - Verify PlayerResourceTracker is present

4. **"Performance issues"**
   - Reduce initial pawn count in GameManager
   - Increase update intervals in UI scripts
   - Check for infinite loops in Console

### Debug Information
- **Console Logs**: Enable "enableDebugLogs" in TestRunner for detailed output
- **Pawn Stats**: Use StatsDisplayUI to see detailed pawn information
- **Resource Summary**: Check PlayerResourceTracker for resource totals
- **Population Stats**: Use PawnGenerator.GetPopulationStats() for demographics

## 📞 Support & Extension

### Adding New Features
The architecture is designed for easy extension:

1. **New Zone Types**: Add to ZoneType enum and update effectiveness calculations
2. **New Resources**: Add to ResourceType enum and update UI displays
3. **New Events**: Create new EventType entries and execution methods
4. **New Pawn Traits**: Extend PawnStats class with new polarity attributes
5. **New UI Panels**: Follow existing UI component patterns

### Code Quality
- All scripts include comprehensive documentation
- Event-driven architecture for loose coupling
- Modular design for easy testing and extension
- Performance optimized for 2-8 players with 20-100 pawns

### Community & Contributions
This is a complete, working foundation for a complex strategy game. The modular architecture makes it easy to:
- Add multiplayer networking
- Integrate AI systems
- Expand with mobile companion apps
- Create complex choice/decision systems
- Build rich NPC interaction mechanics

---
**Rich Billionaire** - Where every citizen is a pawn in your grand design! 🏙️💰

*Built with Unity 2022+ | Fully documented | Production-ready architecture*
